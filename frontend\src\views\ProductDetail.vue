<template>
  <div class="product-detail-page">
    <h2>商品详情测试</h2>
    
    <!-- 测试表单 -->
    <div class="test-form">
      <h3>获取商品详情</h3>
      

      
      <!-- 链接输入 -->
      <div class="form-group">
        <label>商品链接:</label>
        <input
          v-model="productUrl"
          type="text"
          placeholder="请输入商品链接，如：https://item.taobao.com/item.htm?id=123456789 或 https://detail.1688.com/offer/123456789.html"
          class="url-input"
        />
        <p class="help-text">
          支持淘宝、天猫、1688商品详情页链接，系统会自动识别平台类型并提取相关参数
        </p>
      </div>
      
      <button @click="getProductDetail" :disabled="loading" class="test-button">
        {{ loading ? '获取中...' : '获取详情' }}
      </button>
    </div>
    
    <!-- 加载状态 -->
    <div v-if="loading" class="loading">
      <div class="spinner"></div>
      <p>正在获取商品详情...</p>
    </div>
    
    <!-- 错误信息 -->
    <div v-if="error" class="error">
      <h4>获取失败</h4>
      <p class="error-text">{{ error }}</p>
    </div>
    
    <!-- 详情结果 -->
    <div v-if="productDetail" class="detail-result">
      <h3>商品详情</h3>
      
      <!-- 基本信息 -->
      <div class="basic-info">
        <div class="product-images">
          <img
            :src="currentImage || productDetail.picUrl || productDetail.main_image"
            :alt="productDetail.name || productDetail.title"
            @error="handleImageError"
            class="main-image"
          />
          <div v-if="(productDetail.sliderPicUrls || productDetail.images) && (productDetail.sliderPicUrls || productDetail.images).length > 1" class="image-gallery">
            <img
              v-for="(image, index) in (productDetail.sliderPicUrls || productDetail.images).slice(0, 5)"
              :key="index"
              :src="image.url || image"
              :alt="`商品图片${index + 1}`"
              @error="handleImageError"
              @click="changeMainImage(image.url || image)"
              class="gallery-image"
              :class="{ 'active': (currentImage || productDetail.picUrl) === (image.url || image) }"
            />
          </div>
        </div>

        <div class="product-info">
          <h4 class="product-title">{{ productDetail.name || productDetail.title }}</h4>
          <p v-if="productDetail.introduction || productDetail.subtitle" class="product-subtitle">{{ productDetail.introduction || productDetail.subtitle }}</p>

          <div class="price-info">
            <span class="current-price">{{ formatPrice(currentPrice || productDetail.price) }}</span>
            <span v-if="productDetail.marketPrice && productDetail.marketPrice !== (currentPrice || productDetail.price)" class="original-price">{{ formatPrice(productDetail.marketPrice) }}</span>
          </div>

          <div class="meta-info">
            <div class="meta-item" v-if="productDetail.categoryId">
              <strong>分类ID:</strong> {{ productDetail.categoryId }}
            </div>
            <div class="meta-item" v-if="productDetail.salesCount">
              <strong>销量:</strong> {{ productDetail.salesCount }}
            </div>
            <div class="meta-item" v-if="productDetail.stock">
              <strong>库存:</strong> {{ productDetail.stock }}
            </div>
            <div class="meta-item" v-if="productDetail.scores">
              <strong>评分:</strong> {{ productDetail.scores }}
            </div>
          </div>

          <!-- 店铺信息 -->
          <div v-if="productDetail.shopName || productDetail.shop_name" class="shop-info">
            <h5>店铺信息</h5>
            <div class="shop-details">
              <div class="meta-item">
                <strong>店铺名:</strong> {{ productDetail.shopName || productDetail.shop_name }}
              </div>
              <div class="meta-item" v-if="productDetail.source">
                <strong>来源:</strong> {{ productDetail.source }}
              </div>
            </div>
          </div>

          <!-- 配送信息 -->
          <div v-if="productDetail.freight || productDetail.delivery" class="delivery-info">
            <h5>配送信息</h5>
            <div class="delivery-details">
              <div class="meta-item" v-if="productDetail.freight">
                <strong>运费:</strong> {{ formatPrice(productDetail.freight) }}
              </div>
              <div class="meta-item" v-if="productDetail.delivery?.from">
                <strong>发货地:</strong> {{ productDetail.delivery.from }}
              </div>
              <div class="meta-item" v-if="productDetail.delivery?.freight">
                <strong>运费:</strong> {{ productDetail.delivery.freight }}
              </div>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="actions">
            <a :href="productDetail.sourceLink || productDetail.url" target="_blank" class="view-link">查看原商品</a>
          </div>
        </div>
      </div>
      
      <!-- SKU信息 -->
      <div v-if="productDetail.skus && productDetail.skus.length > 0" class="skus-section">
        <h5>商品规格 ({{ productDetail.skus.length }}个)</h5>
        <div class="skus-grid">
          <div
            v-for="(sku, index) in productDetail.skus"
            :key="index"
            class="sku-item"
            :class="{ 'selected': selectedSku && selectedSku.id === sku.id }"
            @click="selectSku(sku)"
          >
            <div class="sku-properties">
              <span
                v-for="(prop, propIndex) in sku.properties"
                :key="propIndex"
                class="sku-property"
              >
                {{ prop.valueName }}
              </span>
            </div>
            <div class="sku-details">
              <span class="sku-price">{{ formatPrice(sku.price) }}</span>
              <span class="sku-stock">库存: {{ sku.stock }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 商品属性 -->
      <div v-if="productDetail.props && productDetail.props.length > 0" class="props-section">
        <h5>商品属性</h5>
        <div class="props-list">
          <div
            v-for="(prop, index) in productDetail.props.slice(0, 10)"
            :key="index"
            class="prop-item"
          >
            <span class="prop-name">{{ prop.name || prop.key }}:</span>
            <span class="prop-value">{{ prop.value }}</span>
          </div>
        </div>
      </div>
      
      <!-- 商品描述 -->
      <div v-if="productDetail.description" class="description-section">
        <h5>商品描述</h5>
        <div class="description-content" v-html="productDetail.description"></div>
      </div>
      
      <!-- 原始数据 -->
      <details class="raw-data">
        <summary>查看原始数据</summary>
        <pre>{{ JSON.stringify(productDetail, null, 2) }}</pre>
      </details>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import { useRoute } from 'vue-router'
import api from '../api'

// 定义props来接收TAB传递的参数
const props = defineProps({
  url: {
    type: String,
    default: ''
  }
})

const productUrl = ref('https://detail.1688.com/offer/899932877120.html')

const loading = ref(false)
const productDetail = ref(null)
const error = ref('')
const selectedSku = ref(null)
const currentPrice = ref(0)
const currentImage = ref('')

const getProductDetail = async () => {
  if (!productUrl.value.trim()) {
    error.value = '请输入商品链接'
    return
  }

  loading.value = true
  error.value = ''
  productDetail.value = null

  try {
    const url = productUrl.value.trim()
    const requestData = { url }

    console.log('获取商品详情，URL:', url)

    // 自动识别平台并选择合适的API
    let response
    if (url.includes('detail.1688.com')) {
      // 1688平台
      console.log('识别为1688平台，使用1688详情API')
      response = await api.product.detail1688(requestData)
    } else if (url.includes('item.taobao.com') || url.includes('detail.tmall.com')) {
      // 淘宝/天猫平台
      console.log('识别为淘宝/天猫平台，使用通用详情API')
      response = await api.product.detailByPlatform({ ...requestData, platform: 'taobao' })
    } else {
      // 使用通用API自动识别
      console.log('使用通用详情API自动识别平台')
      response = await api.product.detailByPlatform(requestData)
    }

    console.log('商品详情响应:', response)

    if (response.code === 200) {
      productDetail.value = response.data
      console.log('商品详情获取成功:', productDetail.value)

      // 初始化选中的SKU和价格
      if (productDetail.value.skus && productDetail.value.skus.length > 0) {
        selectedSku.value = productDetail.value.skus[0]
        currentPrice.value = selectedSku.value.price
      } else {
        currentPrice.value = productDetail.value.price || 0
      }

      // 初始化当前图片
      if (productDetail.value.picUrl) {
        currentImage.value = productDetail.value.picUrl
      }
    } else {
      error.value = response.message || '获取详情失败'
      console.error('商品详情获取失败:', error.value)
    }

  } catch (err) {
    console.error('获取详情错误:', err)
    error.value = err.response?.data?.message || err.message || '网络错误'
  } finally {
    loading.value = false
  }
}

const handleImageError = (event) => {
  event.target.src = 'https://via.placeholder.com/200x200?text=图片加载失败'
}

const formatPrice = (price) => {
  if (!price || price === 0) return '¥0.00'
  // 价格以分为单位，转换为元
  const yuan = price / 100
  return `¥${yuan.toFixed(2)}`
}

const selectSku = (sku) => {
  selectedSku.value = sku
  currentPrice.value = sku.price

  // 如果SKU有图片，更新当前图片
  if (sku.picUrl) {
    currentImage.value = sku.picUrl
  }

  console.log('选中SKU:', sku)
}

const changeMainImage = (imageUrl) => {
  currentImage.value = imageUrl
  console.log('切换主图片:', imageUrl)
}

// 检查URL参数，如果有则自动获取详情
const route = useRoute()

// 初始化URL
const initializeUrl = () => {
  // 优先使用props传递的URL，其次使用路由查询参数
  if (props.url) {
    productUrl.value = props.url
    getProductDetail()
  } else if (route.query.url) {
    productUrl.value = decodeURIComponent(route.query.url)
    getProductDetail()
  }
}

// 监听props变化
watch(() => props.url, (newUrl) => {
  if (newUrl && newUrl !== productUrl.value) {
    productUrl.value = newUrl
    getProductDetail()
  }
}, { immediate: false })

onMounted(() => {
  initializeUrl()
})
</script>

<style scoped>
.product-detail-page {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-form {
  background: white;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  margin-bottom: 24px;
}



.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
}

.url-input {
  width: 100%;
  padding: 12px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  font-size: 14px;
}

.help-text {
  margin-top: 8px;
  font-size: 12px;
  color: #666;
}



.test-button {
  background: #52c41a;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background 0.2s;
}

.test-button:hover:not(:disabled) {
  background: #389e0d;
}

.test-button:disabled {
  background: #d9d9d9;
  cursor: not-allowed;
}

.loading {
  text-align: center;
  padding: 40px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #1890ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error {
  background: #fff2f0;
  border: 1px solid #ffccc7;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 24px;
}

.error h4 {
  color: #cf1322;
  margin: 0 0 8px 0;
}

.error-text {
  color: #cf1322;
  margin: 0;
}

.detail-result {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  overflow: hidden;
}

.detail-result h3 {
  background: #f5f5f5;
  margin: 0;
  padding: 16px 24px;
  border-bottom: 1px solid #d9d9d9;
}

.basic-info {
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: 24px;
  padding: 24px;
}

.product-images {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.main-image {
  width: 100%;
  height: 300px;
  object-fit: cover;
  border-radius: 8px;
  border: 1px solid #f0f0f0;
}

.image-gallery {
  display: flex;
  gap: 8px;
  overflow-x: auto;
}

.gallery-image {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: 4px;
  border: 1px solid #f0f0f0;
  flex-shrink: 0;
  cursor: pointer;
  transition: all 0.2s;
}

.gallery-image:hover {
  border-color: #1890ff;
}

.gallery-image.active {
  border-color: #1890ff;
  border-width: 2px;
}

.product-title {
  font-size: 18px;
  font-weight: bold;
  margin: 0 0 8px 0;
  line-height: 1.4;
}

.product-subtitle {
  color: #666;
  margin: 0 0 16px 0;
  font-size: 14px;
}

.price-info {
  margin-bottom: 20px;
}

.current-price {
  font-size: 24px;
  font-weight: bold;
  color: #e74c3c;
  margin-right: 12px;
}

.original-price {
  font-size: 16px;
  color: #999;
  text-decoration: line-through;
}

.meta-info,
.shop-details,
.delivery-details {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
  margin-bottom: 20px;
}

.meta-item {
  font-size: 14px;
  color: #333;
}

.shop-info,
.delivery-info {
  border-top: 1px solid #f0f0f0;
  padding-top: 16px;
  margin-top: 16px;
}

.shop-info h5,
.delivery-info h5 {
  margin: 0 0 12px 0;
  font-size: 16px;
  color: #333;
}

.actions {
  margin-top: 20px;
}

.view-link {
  display: inline-block;
  background: #1890ff;
  color: white;
  padding: 8px 16px;
  border-radius: 4px;
  text-decoration: none;
  transition: background 0.2s;
}

.view-link:hover {
  background: #096dd9;
}

.skus-section,
.props-section,
.description-section {
  border-top: 1px solid #f0f0f0;
  padding: 24px;
}

.skus-section h5,
.props-section h5,
.description-section h5 {
  margin: 0 0 16px 0;
  font-size: 16px;
}

.skus-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 12px;
}

.sku-item {
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  padding: 12px;
  background: #fafafa;
  cursor: pointer;
  transition: all 0.2s;
}

.sku-item:hover {
  border-color: #1890ff;
  background: #f6ffed;
}

.sku-item.selected {
  border-color: #1890ff;
  background: #e6f7ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
}

.sku-properties {
  display: flex;
  gap: 12px;
  margin-bottom: 8px;
}

.sku-property {
  background: #e6f7ff;
  color: #1890ff;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.sku-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.sku-price {
  font-weight: bold;
  color: #e74c3c;
  font-size: 16px;
}

.sku-stock {
  color: #666;
  font-size: 14px;
}

.props-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 12px;
}

.prop-item {
  display: flex;
  font-size: 14px;
}

.prop-name {
  font-weight: 500;
  margin-right: 8px;
  min-width: 80px;
}

.prop-value {
  color: #666;
}

.description-content {
  font-size: 14px;
  line-height: 1.6;
  color: #333;
}

.raw-data {
  border-top: 1px solid #f0f0f0;
  padding: 24px;
}

.raw-data summary {
  cursor: pointer;
  font-weight: 500;
  margin-bottom: 16px;
}

.raw-data pre {
  background: #f5f5f5;
  padding: 16px;
  border-radius: 4px;
  overflow-x: auto;
  font-size: 12px;
  line-height: 1.4;
}

@media (max-width: 768px) {
  .basic-info {
    grid-template-columns: 1fr;
  }

  .meta-info,
  .shop-details,
  .delivery-details {
    grid-template-columns: 1fr;
  }
}
</style>
