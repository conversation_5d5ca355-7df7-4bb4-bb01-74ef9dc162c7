"""
爬虫管理器

提供多平台爬虫的统一管理和调度功能
"""

import asyncio
import time
from typing import Dict, List, Optional, Any
from concurrent.futures import ThreadPoolExecutor
from dataclasses import dataclass

from .config import CrawlerConfig, get_default_config
from .platforms import get_crawler_class
from .base import CrawlerResult, SearchResult


@dataclass
class SearchRequest:
    """搜索请求"""
    query: str
    platforms: List[str]
    max_pages: int = 1
    max_results: int = 50
    timeout: int = 30


@dataclass
class SearchResponse:
    """搜索响应"""
    query: str
    total_results: int
    results: List[SearchResult]
    platform_results: Dict[str, CrawlerResult]
    success: bool
    error_message: Optional[str] = None
    response_time: Optional[float] = None


class CrawlerManager:
    """爬虫管理器"""
    
    def __init__(self, config: Optional[CrawlerConfig] = None):
        self.config = config or get_default_config()
        self.active_crawlers = {}
        self.stats = {
            "total_searches": 0,
            "successful_searches": 0,
            "failed_searches": 0,
            "total_results": 0,
            "platform_stats": {}
        }
        
        # 限流器
        self.rate_limiter = {}
        self.last_request_time = {}
    
    async def search(self, request: SearchRequest) -> SearchResponse:
        """执行多平台搜索"""
        start_time = time.time()
        self.stats["total_searches"] += 1
        
        try:
            # 验证平台
            valid_platforms = self._validate_platforms(request.platforms)
            if not valid_platforms:
                raise ValueError("没有有效的平台")
            
            # 并发搜索
            platform_results = await self._search_platforms(
                request.query, 
                valid_platforms, 
                request.max_pages,
                request.timeout
            )
            
            # 合并结果
            all_results = []
            for platform, result in platform_results.items():
                if result.success:
                    all_results.extend(result.products)
            
            # 限制结果数量
            if request.max_results > 0:
                all_results = all_results[:request.max_results]
            
            # 统计
            response_time = time.time() - start_time
            self.stats["successful_searches"] += 1
            self.stats["total_results"] += len(all_results)
            
            return SearchResponse(
                query=request.query,
                total_results=len(all_results),
                results=all_results,
                platform_results=platform_results,
                success=True,
                response_time=response_time
            )
            
        except Exception as e:
            # 统计
            response_time = time.time() - start_time
            self.stats["failed_searches"] += 1
            
            return SearchResponse(
                query=request.query,
                total_results=0,
                results=[],
                platform_results={},
                success=False,
                error_message=str(e),
                response_time=response_time
            )
    
    async def _search_platforms(
        self, 
        query: str, 
        platforms: List[str], 
        max_pages: int,
        timeout: int
    ) -> Dict[str, CrawlerResult]:
        """并发搜索多个平台"""
        
        # 创建搜索任务
        tasks = []
        for platform in platforms:
            task = asyncio.create_task(
                self._search_single_platform(query, platform, max_pages, timeout)
            )
            tasks.append((platform, task))
        
        # 等待所有任务完成
        results = {}
        for platform, task in tasks:
            try:
                result = await asyncio.wait_for(task, timeout=timeout)
                results[platform] = result
            except asyncio.TimeoutError:
                results[platform] = CrawlerResult(
                    platform=platform,
                    query=query,
                    total_found=0,
                    products=[],
                    success=False,
                    error_message="搜索超时"
                )
            except Exception as e:
                results[platform] = CrawlerResult(
                    platform=platform,
                    query=query,
                    total_found=0,
                    products=[],
                    success=False,
                    error_message=str(e)
                )
        
        return results
    
    async def _search_single_platform(
        self, 
        query: str, 
        platform: str, 
        max_pages: int,
        timeout: int
    ) -> CrawlerResult:
        """搜索单个平台"""
        
        # 限流检查
        await self._rate_limit_check(platform)
        
        # 获取爬虫类
        crawler_class = get_crawler_class(platform)
        if not crawler_class:
            raise ValueError(f"不支持的平台: {platform}")
        
        # 创建爬虫实例
        async with crawler_class(self.config) as crawler:
            result = await crawler.search(query, max_pages)
            
            # 更新平台统计
            self._update_platform_stats(platform, result)
            
            return result
    
    async def _rate_limit_check(self, platform: str):
        """限流检查"""
        current_time = time.time()
        last_time = self.last_request_time.get(platform, 0)
        
        # 计算需要等待的时间
        min_interval = 1.0 / self.config.rate_limit  # 每秒最大请求数
        elapsed = current_time - last_time
        
        if elapsed < min_interval:
            wait_time = min_interval - elapsed
            await asyncio.sleep(wait_time)
        
        self.last_request_time[platform] = time.time()
    
    def _validate_platforms(self, platforms: List[str]) -> List[str]:
        """验证平台列表"""
        valid_platforms = []
        for platform in platforms:
            if get_crawler_class(platform):
                valid_platforms.append(platform)
        return valid_platforms
    
    def _update_platform_stats(self, platform: str, result: CrawlerResult):
        """更新平台统计"""
        if platform not in self.stats["platform_stats"]:
            self.stats["platform_stats"][platform] = {
                "total_searches": 0,
                "successful_searches": 0,
                "failed_searches": 0,
                "total_results": 0,
                "average_response_time": 0.0
            }
        
        stats = self.stats["platform_stats"][platform]
        stats["total_searches"] += 1
        
        if result.success:
            stats["successful_searches"] += 1
            stats["total_results"] += result.total_found
        else:
            stats["failed_searches"] += 1
        
        # 更新平均响应时间
        if result.response_time:
            current_avg = stats["average_response_time"]
            total_searches = stats["total_searches"]
            stats["average_response_time"] = (
                (current_avg * (total_searches - 1) + result.response_time) / total_searches
            )
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        total_searches = max(self.stats["total_searches"], 1)
        success_rate = self.stats["successful_searches"] / total_searches * 100
        
        return {
            "total_searches": self.stats["total_searches"],
            "successful_searches": self.stats["successful_searches"],
            "failed_searches": self.stats["failed_searches"],
            "success_rate": round(success_rate, 2),
            "total_results": self.stats["total_results"],
            "average_results_per_search": round(
                self.stats["total_results"] / total_searches, 2
            ),
            "platform_stats": self.stats["platform_stats"]
        }
    
    def get_supported_platforms(self) -> List[str]:
        """获取支持的平台列表"""
        from .platforms import PLATFORM_CRAWLERS
        return list(PLATFORM_CRAWLERS.keys())
    
    async def test_platform(self, platform: str) -> Dict[str, Any]:
        """测试平台可用性"""
        try:
            crawler_class = get_crawler_class(platform)
            if not crawler_class:
                return {
                    "platform": platform,
                    "available": False,
                    "error": "不支持的平台"
                }
            
            # 使用简单查询测试
            async with crawler_class(self.config) as crawler:
                result = await crawler.search("测试", max_pages=1)
                
                return {
                    "platform": platform,
                    "available": result.success,
                    "response_time": result.response_time,
                    "results_count": result.total_found,
                    "error": result.error_message if not result.success else None
                }
                
        except Exception as e:
            return {
                "platform": platform,
                "available": False,
                "error": str(e)
            }
    
    async def close(self):
        """关闭管理器"""
        # 关闭所有活跃的爬虫
        for crawler in self.active_crawlers.values():
            try:
                await crawler.close()
            except Exception as e:
                print(f"关闭爬虫失败: {str(e)}")
        
        self.active_crawlers.clear()


# 全局爬虫管理器实例
_crawler_manager = None


def get_crawler_manager() -> CrawlerManager:
    """获取全局爬虫管理器实例"""
    global _crawler_manager
    if _crawler_manager is None:
        _crawler_manager = CrawlerManager()
    return _crawler_manager
