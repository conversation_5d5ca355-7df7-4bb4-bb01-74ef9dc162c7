#!/usr/bin/env python3
"""
创建爬虫池管理相关数据表
"""

import sys
import os
from urllib.parse import urlparse

# 添加backend目录到Python路径
backend_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, os.path.dirname(backend_dir))

from sqlalchemy import create_engine, text
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

def create_crawler_pool_tables():
    """创建爬虫池管理相关表"""
    try:
        # 从环境变量获取数据库URL
        database_url = os.getenv("DATABASE_URL", "mysql+pymysql://root:123456@*************:3306/agent_crawler")

        # 解析数据库URL
        parsed = urlparse(database_url.replace('mysql+pymysql://', 'mysql://'))

        print(f"连接数据库: {parsed.hostname}:{parsed.port}/{parsed.path[1:]}")

        # 创建数据库引擎
        engine = create_engine(database_url, echo=True)

        # 测试连接
        with engine.connect() as connection:
            connection.execute(text("SELECT 1"))
            print("✅ 数据库连接成功")

        print("开始创建爬虫池管理表...")

        # 1. 会话管理表
        create_session_table = """
        CREATE TABLE IF NOT EXISTS crawler_sessions (
            id BIGINT PRIMARY KEY AUTO_INCREMENT,
            session_id VARCHAR(64) NOT NULL UNIQUE COMMENT '会话ID',
            client_ip VARCHAR(45) NOT NULL COMMENT '客户端IP',
            user_agent TEXT COMMENT 'User-Agent',
            platform_code VARCHAR(50) NOT NULL COMMENT '平台代码',
            account_id BIGINT COMMENT '绑定的爬虫账号ID',
            proxy_id BIGINT COMMENT '使用的代理ID',

            -- 会话状态
            status ENUM('active', 'expired', 'blocked', 'error') DEFAULT 'active' COMMENT '会话状态',
            request_count INT DEFAULT 0 COMMENT '会话请求计数',
            success_count INT DEFAULT 0 COMMENT '成功请求计数',
            error_count INT DEFAULT 0 COMMENT '错误请求计数',

            -- 反爬检测
            risk_score DECIMAL(5,2) DEFAULT 0.00 COMMENT '风险评分(0-100)',
            last_request_at DATETIME COMMENT '最后请求时间',
            blocked_until DATETIME COMMENT '阻塞到期时间',

            -- 时间戳
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            expires_at DATETIME COMMENT '会话过期时间',

            INDEX idx_session_id (session_id),
            INDEX idx_client_ip (client_ip),
            INDEX idx_platform_account (platform_code, account_id),
            INDEX idx_status_expires (status, expires_at),
            INDEX idx_last_request (last_request_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='爬虫会话管理表'
        """
        
        # 2. 爬虫池调度表
        create_pool_schedule_table = """
        CREATE TABLE IF NOT EXISTS crawler_pool_schedule (
            id BIGINT PRIMARY KEY AUTO_INCREMENT,
            platform_code VARCHAR(50) NOT NULL COMMENT '平台代码',
            account_id BIGINT NOT NULL COMMENT '爬虫账号ID',
            
            -- 调度状态
            status ENUM('available', 'busy', 'cooling', 'blocked', 'maintenance') DEFAULT 'available' COMMENT '调度状态',
            priority_score DECIMAL(8,2) DEFAULT 100.00 COMMENT '优先级评分',
            load_factor DECIMAL(5,2) DEFAULT 0.00 COMMENT '负载因子(0-1)',
            
            -- 使用统计
            current_sessions INT DEFAULT 0 COMMENT '当前会话数',
            max_concurrent_sessions INT DEFAULT 5 COMMENT '最大并发会话数',
            requests_per_minute INT DEFAULT 0 COMMENT '每分钟请求数',
            max_requests_per_minute INT DEFAULT 60 COMMENT '每分钟最大请求数',
            
            -- 健康度指标
            health_score DECIMAL(5,2) DEFAULT 100.00 COMMENT '健康度评分(0-100)',
            success_rate_1h DECIMAL(5,2) DEFAULT 100.00 COMMENT '1小时成功率',
            success_rate_24h DECIMAL(5,2) DEFAULT 100.00 COMMENT '24小时成功率',
            avg_response_time_ms INT DEFAULT 0 COMMENT '平均响应时间(毫秒)',
            
            -- 冷却和限制
            last_used_at DATETIME COMMENT '最后使用时间',
            cooling_until DATETIME COMMENT '冷却到期时间',
            blocked_until DATETIME COMMENT '阻塞到期时间',
            next_available_at DATETIME COMMENT '下次可用时间',
            
            -- 时间戳
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            
            UNIQUE KEY uk_platform_account (platform_code, account_id),
            INDEX idx_status_priority (status, priority_score DESC),
            INDEX idx_platform_status (platform_code, status),
            INDEX idx_next_available (next_available_at),
            INDEX idx_health_score (health_score DESC)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='爬虫池调度表';
        """
        
        # 3. 请求频率控制表
        create_rate_limit_table = """
        CREATE TABLE IF NOT EXISTS crawler_rate_limits (
            id BIGINT PRIMARY KEY AUTO_INCREMENT,
            identifier VARCHAR(100) NOT NULL COMMENT '标识符(IP/账号/会话)',
            identifier_type ENUM('ip', 'account', 'session', 'platform') NOT NULL COMMENT '标识符类型',
            platform_code VARCHAR(50) COMMENT '平台代码',
            
            -- 频率限制
            window_size_seconds INT NOT NULL DEFAULT 60 COMMENT '时间窗口(秒)',
            max_requests INT NOT NULL DEFAULT 60 COMMENT '最大请求数',
            current_requests INT DEFAULT 0 COMMENT '当前请求数',
            
            -- 时间窗口
            window_start_at DATETIME NOT NULL COMMENT '窗口开始时间',
            window_end_at DATETIME NOT NULL COMMENT '窗口结束时间',
            
            -- 违规记录
            violation_count INT DEFAULT 0 COMMENT '违规次数',
            last_violation_at DATETIME COMMENT '最后违规时间',
            blocked_until DATETIME COMMENT '阻塞到期时间',
            
            -- 时间戳
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            
            UNIQUE KEY uk_identifier_type_platform (identifier, identifier_type, platform_code),
            INDEX idx_window_end (window_end_at),
            INDEX idx_blocked_until (blocked_until),
            INDEX idx_platform_type (platform_code, identifier_type)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='请求频率控制表';
        """
        
        # 4. 爬虫性能监控表
        create_performance_table = """
        CREATE TABLE IF NOT EXISTS crawler_performance_metrics (
            id BIGINT PRIMARY KEY AUTO_INCREMENT,
            account_id BIGINT NOT NULL COMMENT '爬虫账号ID',
            platform_code VARCHAR(50) NOT NULL COMMENT '平台代码',
            
            -- 性能指标
            metric_type ENUM('response_time', 'success_rate', 'error_rate', 'throughput') NOT NULL COMMENT '指标类型',
            metric_value DECIMAL(10,4) NOT NULL COMMENT '指标值',
            metric_unit VARCHAR(20) COMMENT '指标单位',
            
            -- 时间维度
            time_window ENUM('1m', '5m', '15m', '1h', '6h', '24h') NOT NULL COMMENT '时间窗口',
            window_start_at DATETIME NOT NULL COMMENT '窗口开始时间',
            window_end_at DATETIME NOT NULL COMMENT '窗口结束时间',
            
            -- 采样信息
            sample_count INT DEFAULT 1 COMMENT '样本数量',
            min_value DECIMAL(10,4) COMMENT '最小值',
            max_value DECIMAL(10,4) COMMENT '最大值',
            avg_value DECIMAL(10,4) COMMENT '平均值',
            
            -- 时间戳
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            
            INDEX idx_account_metric_window (account_id, metric_type, time_window),
            INDEX idx_platform_metric_window (platform_code, metric_type, time_window),
            INDEX idx_window_start (window_start_at),
            INDEX idx_window_end (window_end_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='爬虫性能监控表';
        """
        
        # 5. 反爬检测记录表
        create_anti_crawl_table = """
        CREATE TABLE IF NOT EXISTS anti_crawl_detections (
            id BIGINT PRIMARY KEY AUTO_INCREMENT,
            account_id BIGINT COMMENT '爬虫账号ID',
            session_id VARCHAR(64) COMMENT '会话ID',
            client_ip VARCHAR(45) NOT NULL COMMENT '客户端IP',
            platform_code VARCHAR(50) NOT NULL COMMENT '平台代码',
            
            -- 检测信息
            detection_type ENUM('captcha', 'rate_limit', 'ip_block', 'account_block', 'suspicious_pattern') NOT NULL COMMENT '检测类型',
            severity ENUM('low', 'medium', 'high', 'critical') DEFAULT 'medium' COMMENT '严重程度',
            confidence DECIMAL(5,2) DEFAULT 50.00 COMMENT '置信度(0-100)',
            
            -- 详细信息
            detection_details JSON COMMENT '检测详情',
            response_code INT COMMENT '响应状态码',
            response_headers JSON COMMENT '响应头信息',
            error_message TEXT COMMENT '错误信息',
            
            -- 处理状态
            status ENUM('detected', 'confirmed', 'resolved', 'ignored') DEFAULT 'detected' COMMENT '处理状态',
            auto_resolved BOOLEAN DEFAULT FALSE COMMENT '是否自动解决',
            resolved_at DATETIME COMMENT '解决时间',
            
            -- 时间戳
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            
            INDEX idx_account_platform (account_id, platform_code),
            INDEX idx_client_ip_platform (client_ip, platform_code),
            INDEX idx_detection_type_severity (detection_type, severity),
            INDEX idx_status_created (status, created_at),
            INDEX idx_session_id (session_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='反爬检测记录表';
        """
        
        # 执行创建表的SQL
        tables = [
            ("crawler_sessions", create_session_table),
            ("crawler_pool_schedule", create_pool_schedule_table),
            ("crawler_rate_limits", create_rate_limit_table),
            ("crawler_performance_metrics", create_performance_table),
            ("anti_crawl_detections", create_anti_crawl_table)
        ]

        with engine.connect() as connection:
            # 开始事务
            trans = connection.begin()
            try:
                for table_name, sql in tables:
                    print(f"创建表: {table_name}")
                    connection.execute(text(sql))
                    print(f"✅ 表 {table_name} 创建成功")

                # 提交事务
                trans.commit()
                print("🎉 所有爬虫池管理表创建完成！")

            except Exception as e:
                # 回滚事务
                trans.rollback()
                print(f"❌ 创建表失败: {e}")
                raise

    except Exception as e:
        print(f"❌ 数据库操作失败: {e}")
        raise

if __name__ == "__main__":
    create_crawler_pool_tables()
