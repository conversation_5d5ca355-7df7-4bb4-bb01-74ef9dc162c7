"""
淘宝登录服务

专门处理淘宝平台的登录功能（不包含Token刷新）
"""

import re
from datetime import datetime, timedelta
from typing import Dict, Optional, Tuple
from playwright.sync_api import Page

from .base_login import BasePlatformLogin


class TaobaoLogin(BasePlatformLogin):
    """淘宝登录服务"""

    def __init__(self):
        super().__init__("taobao")

    def get_login_url(self) -> str:
        """获取淘宝登录页面URL"""
        return "https://login.taobao.com/member/login.jhtml"
        # return "https://httpbin.org/ip"

    def get_login_selectors(self) -> Dict[str, str]:
        """获取淘宝登录页面选择器"""
        return {
            "username": "#fm-login-id",  # 用户名输入框
            "password": "#fm-login-password",  # 密码输入框
            "login_button": "#login-form .fm-button"  # 登录按钮
        }

    def get_success_indicators(self) -> list:
        """获取淘宝登录成功的URL指示器"""
        return [
            "www.taobao.com",
            "i.taobao.com", 
            "member.taobao.com"
        ]

    def get_verification_indicators(self) -> list:
        """获取淘宝验证过程的URL指示器"""
        return [
            "checkcode", "verify", "challenge", "captcha",
            "slider", "sms", "phone", "mobile"
        ]

    def extract_token_from_cookies(self, cookies: list) -> Tuple[Optional[str], Optional[datetime]]:
        """从淘宝Cookie中提取Token和过期时间"""
        token = None
        token_expires_at = None

        for cookie in cookies:
            if cookie['name'] == '_m_h5_tk':
                m_h5_tk = cookie['value']
                if '_' in m_h5_tk:
                    token_parts = m_h5_tk.split('_')
                    token = token_parts[0]
                    
                    # 提取过期时间
                    if len(token_parts) > 1:
                        try:
                            expires_timestamp = int(token_parts[1]) / 1000
                            token_expires_at = datetime.fromtimestamp(expires_timestamp)
                        except (ValueError, IndexError):
                            # 如果无法解析过期时间，设置为24小时后过期
                            token_expires_at = datetime.now() + timedelta(hours=24)
                    break

        return token, token_expires_at

    def get_critical_cookies(self) -> list:
        """获取淘宝关键Cookie列表"""
        return [
            'arms_uid', 'thw', 'cna', 't', '3PcFlag', 'xlly_s', '_bl_uid',
            'wk_cookie2', 'wk_unb', '_hvn_lgc_', 'havana_lgc2_0', 'lgc',
            'cancelledSubSites', 'dnk', 'tracknick', 'lid', 'sn', 'isg',
            'cdpid', 'cnaui', 'aui', 'ubn', 'ucn', 'mtop_partitioned_detect',
            '_m_h5_tk', '_m_h5_tk_enc', 'cbc', 'bxuab', 'XSRF-TOKEN',
            '_samesite_flag_', 'cookie2', '_tb_token_', 'unb', 'uc3', 'csg',
            'cookie17', 'skt', 'existShop', 'uc4', 'lc', '_cc_', '_l_g_',
            'sg', '_nk_', 'cookie1', 'sgcookie', 'havana_lgc_exp', 'fastSlient',
            'uc1', 'sca', 'sdkSilent', 'havana_sdkSilent', 'login', 'tbsa',
            'atpsida', 'tfstk'
        ]

    def validate_login_state(self, page: Page) -> bool:
        """验证淘宝登录状态"""
        try:
            current_url = page.url
            
            # 检查是否在登录页面
            if "login.taobao.com" in current_url or "passport.taobao.com" in current_url:
                return False
            
            # 检查是否在成功页面
            success_indicators = self.get_success_indicators()
            if any(indicator in current_url for indicator in success_indicators):
                return True
                
            return False
            
        except Exception as e:
            print(f"验证淘宝登录状态失败: {str(e)}")
            return False

    def filter_cookies(self, cookie_string: str) -> str:
        """过滤淘宝不必要的Cookie"""
        if not cookie_string:
            return cookie_string

        try:
            # 分割Cookie
            cookies = cookie_string.split(';')
            critical_cookies = self.get_critical_cookies()

            # 只保留关键Cookie
            filtered_cookies = []
            for cookie in cookies:
                cookie = cookie.strip()
                if '=' in cookie:
                    name = cookie.split('=')[0].strip()
                    if name in critical_cookies:
                        filtered_cookies.append(cookie)

            # 如果没有获取到任何关键Cookie，返回原始Cookie字符串
            if not filtered_cookies:
                print("警告: 未获取到任何关键Cookie，返回原始Cookie")
                return cookie_string

            result = '; '.join(filtered_cookies)
            print(f"淘宝Cookie过滤: {len(cookies)} -> {len(filtered_cookies)} 个关键Cookie")
            return result

        except Exception as e:
            print(f"过滤淘宝Cookie失败: {str(e)}，返回原始Cookie")
            return cookie_string
