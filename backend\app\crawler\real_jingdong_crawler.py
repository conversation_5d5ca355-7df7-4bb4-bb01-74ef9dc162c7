"""
真实的京东爬虫实现 - 支持多种方案
优先使用Selenium，回退到requests和Playwright
"""
import asyncio
import time
import random
import platform
from typing import List, Dict, Optional
from urllib.parse import quote, urlencode

# 检测系统类型
SYSTEM_TYPE = platform.system().lower()
IS_WINDOWS = SYSTEM_TYPE == "windows"
IS_LINUX = SYSTEM_TYPE == "linux"

print(f"🖥️ 检测到系统类型: {SYSTEM_TYPE}")

# Windows系统使用requests方案
if IS_WINDOWS:
    print("🔧 Windows系统 - 使用requests+BeautifulSoup方案")
    import requests
    from bs4 import BeautifulSoup
    import re

# Linux系统使用Playwright方案
elif IS_LINUX:
    print("🔧 Linux系统 - 使用Playwright方案")
    try:
        from playwright.async_api import async_playwright, Browser, Page
        PLAYWRIGHT_AVAILABLE = True
        print("✅ Playwright 可用")
    except ImportError:
        print("❌ Playwright 未安装，回退到requests方案")
        PLAYWRIGHT_AVAILABLE = False
        import requests
        from bs4 import BeautifulSoup
        import re

# 其他系统
else:
    print("🔧 其他系统 - 使用requests方案")
    import requests
    from bs4 import BeautifulSoup
    import re


class RealJingdongCrawler:
    """真实的京东爬虫"""

    def __init__(self):
        self.system_type = SYSTEM_TYPE
        self.use_playwright = IS_LINUX and PLAYWRIGHT_AVAILABLE
        self.session = None
        self.browser = None
        self.base_url = "https://www.jd.com"
        self.search_url = "https://search.jd.com/Search"
        
        print(f"🚀 京东爬虫初始化 - 系统: {self.system_type}, 使用Playwright: {self.use_playwright}")
        
        if not self.use_playwright:
            self._init_requests_session()
    
    def _init_requests_session(self):
        """初始化requests会话"""
        self.session = requests.Session()
        
        # 设置请求头
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })

    async def search_products(self, query: str, account_username: str = None) -> Dict:
        """搜索商品 - 优先使用Selenium方案"""
        try:
            # 优先尝试Selenium方案
            try:
                from .selenium_jingdong_crawler import get_selenium_jingdong_crawler
                print("🔧 尝试使用Selenium+ChromeDriver方案...")
                selenium_crawler = await get_selenium_jingdong_crawler()
                result = await selenium_crawler.search_products(query, account_username)
                
                # 如果Selenium成功获取到数据，直接返回
                if result.get('total', 0) > 0:
                    print("✅ Selenium方案成功获取京东数据")
                    return result
                else:
                    print("⚠️ Selenium方案未获取到京东数据，尝试其他方案")
                    
            except Exception as selenium_error:
                print(f"⚠️ Selenium方案失败: {str(selenium_error)}")
            
            # 回退到原有方案
            if self.use_playwright:
                print("🔧 回退到Playwright方案...")
                return await self._search_with_playwright(query, account_username)
            else:
                print("🔧 回退到requests方案...")
                return await self._search_with_requests(query, account_username)

        except Exception as e:
            print(f"❌ 所有京东爬虫方案都失败: {str(e)}")
            return self._get_fallback_data(query, f"爬虫错误: {str(e)}", account_username)

    async def _search_with_requests(self, query: str, account_username: str = None) -> Dict:
        """使用requests方案搜索"""
        try:
            print(f"🔍 [Requests] 开始搜索京东商品: {query}")
            
            # 构建搜索URL
            print(f"🔍 [Requests] 原始查询词: '{query}' (类型: {type(query)})")

            # 确保查询词是有效的字符串
            if not query or query.strip() == '':
                return self._get_fallback_data(query, "查询词为空", account_username)

            # 使用quote进行URL编码
            encoded_query = quote(query.strip(), safe='')
            search_url = f"{self.search_url}?keyword={encoded_query}&enc=utf-8"

            print(f"🔍 [Requests] 编码后查询词: '{encoded_query}'")
            print(f"🔍 [Requests] 正在访问京东搜索页面: {search_url}")
            
            # 发送请求
            response = self.session.get(search_url, timeout=30)
            response.raise_for_status()
            
            # 解析HTML
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 保存页面内容用于调试
            safe_query = re.sub(r'[^\w\-_]', '_', query)
            debug_filename = f"debug_jingdong_{safe_query}.html"
            try:
                with open(debug_filename, "w", encoding="utf-8") as f:
                    f.write(response.text)
                print(f"📄 京东页面内容已保存到: {debug_filename}")
            except Exception as e:
                print(f"⚠️ 保存调试文件失败: {str(e)}")
            
            print(f"📄 京东页面内容长度: {len(response.text)} 字符")
            
            # 检查页面内容的关键信息
            if "登录" in response.text:
                print("🔐 京东页面包含'登录'关键词")
            if "验证" in response.text:
                print("🔒 京东页面包含'验证'关键词")
            if len(response.text) < 10000:
                print("⚠️ 京东页面内容过短，可能被反爬虫拦截")

            # 解析商品列表
            products = self._parse_products_requests(soup, response.text)
            
            if products:
                result = {
                    "platform": "jingdong",
                    "query": query,
                    "total": len(products),
                    "products": products,
                    "response_time": 1.5,
                    "account_used": account_username,
                    "crawl_method": "requests+BeautifulSoup"
                }
                print(f"✅ 京东搜索完成，找到 {len(products)} 个商品")
                return result
            else:
                return self._get_fallback_data(query, "未找到商品", account_username)
                
        except Exception as e:
            print(f"❌ requests搜索京东失败: {str(e)}")
            return self._get_fallback_data(query, f"requests错误: {str(e)}", account_username)

    def _parse_products_requests(self, soup, response_text: str = "") -> List[Dict]:
        """解析商品信息 - requests方案"""
        try:
            print("📄 开始解析京东商品信息...")
            
            # 尝试多种京东商品选择器
            item_selectors = [
                '.gl-item',           # 主要商品容器
                '.goods-item',        # 备用商品容器
                '.item',              # 通用商品容器
                '.s-item',            # 搜索结果商品
                '.product-item',      # 产品项目
                '.list-item',         # 列表项目
                '[data-sku]'          # 带SKU的商品
            ]
            
            items = []
            for selector in item_selectors:
                items = soup.select(selector)
                print(f"🔍 尝试选择器 '{selector}': 找到 {len(items)} 个元素")
                if items:
                    print(f"✅ 找到京东商品元素，使用选择器: {selector}")
                    break

            if not items:
                print("⚠️ 未找到京东商品元素，可能页面结构已变化")
                # 尝试查找页面中的关键元素
                print("🔍 京东页面调试信息:")
                print(f"  - 页面标题: {soup.title.string if soup.title else '无标题'}")
                print(f"  - 是否包含'商品': {'商品' in response_text}")
                print(f"  - 是否包含'京东': {'京东' in response_text}")
                print(f"  - 是否包含'gl-item': {'gl-item' in response_text}")
                
                # 查找所有可能的商品容器
                all_divs = soup.find_all('div', class_=True)
                class_names = set()
                for div in all_divs[:50]:  # 只检查前50个
                    if div.get('class'):
                        class_names.update(div.get('class'))
                
                print(f"  - 页面中的CSS类名示例: {list(class_names)[:20]}")
                return []

            products = []
            
            for i, item in enumerate(items):
                try:
                    product = self._parse_single_product_requests(item)
                    if product:
                        products.append(product)
                        print(f"✅ 解析京东商品 {i+1}: {product['title'][:30]}...")
                        
                except Exception as e:
                    print(f"⚠️ 解析京东商品 {i+1} 失败: {str(e)}")
                    continue
            
            print(f"📊 成功解析 {len(products)} 个京东商品")
            return products
            
        except Exception as e:
            print(f"❌ 解析京东商品信息失败: {str(e)}")
            return []

    def _parse_single_product_requests(self, item) -> Optional[Dict]:
        """解析单个商品信息 - requests方案"""
        try:
            # 商品标题
            title_selectors = ['.p-name a em', '.p-name a', '.name a', '.p-name']
            title = ""
            for selector in title_selectors:
                title_elem = item.select_one(selector)
                if title_elem:
                    title = title_elem.get_text().strip()
                    break
            
            if not title:
                return None
            
            # 商品价格
            price_selectors = ['.p-price i', '.price i', '.p-price', '.price-now']
            price_text = ""
            for selector in price_selectors:
                price_elem = item.select_one(selector)
                if price_elem:
                    price_text = price_elem.get_text().strip()
                    break
            
            price = self._clean_price(price_text)
            
            # 商品链接
            link_selectors = ['.p-name a', '.name a', 'a']
            product_url = ""
            for selector in link_selectors:
                link_elem = item.select_one(selector)
                if link_elem:
                    href = link_elem.get('href')
                    if href:
                        if href.startswith('//'):
                            product_url = f"https:{href}"
                        elif href.startswith('/'):
                            product_url = f"https://www.jd.com{href}"
                        elif href.startswith('http'):
                            product_url = href
                        break
            
            # 商品图片
            img_selectors = ['.p-img img', '.img img', 'img']
            image_url = ""
            for selector in img_selectors:
                img_elem = item.select_one(selector)
                if img_elem:
                    for attr in ['src', 'data-lazy-img', 'data-original', 'data-src']:
                        img_url = img_elem.get(attr)
                        if img_url:
                            if img_url.startswith('//'):
                                image_url = f"https:{img_url}"
                            elif img_url.startswith('/'):
                                image_url = f"https://www.jd.com{img_url}"
                            elif img_url.startswith('http'):
                                image_url = img_url
                            break
                    if image_url:
                        break
            
            # 店铺名称
            shop_selectors = ['.p-shop a', '.shop a', '.store-name', '.p-shopnum a']
            shop_name = ""
            for selector in shop_selectors:
                shop_elem = item.select_one(selector)
                if shop_elem:
                    shop_name = shop_elem.get_text().strip()
                    break
            
            # 评论数/销量
            comment_selectors = ['.p-commit a', '.comment-count', '.p-commit']
            comments = ""
            for selector in comment_selectors:
                comment_elem = item.select_one(selector)
                if comment_elem:
                    comments = comment_elem.get_text().strip()
                    break
            
            return {
                "title": title,
                "price": price,
                "image": image_url or "https://via.placeholder.com/200x200?text=NoImage",
                "link": product_url or "#",
                "platform": "jingdong",
                "shop_name": shop_name or "京东自营",
                "sales": comments or "评论未知",
                "location": "全国",
                "is_post_free": "包邮"
            }
            
        except Exception as e:
            print(f"解析单个京东商品失败: {str(e)}")
            return None

    def _clean_price(self, price_text: str) -> str:
        """清理价格文本"""
        try:
            if not price_text:
                return "价格面议"
            
            # 移除换行符和空格
            price = price_text.replace('\n', '').replace('\r', '').strip()
            
            # 提取数字和小数点
            price_match = re.search(r'[\d,]+\.?\d*', price)
            if price_match:
                price_num = price_match.group()
                return f"¥{price_num}"
            
            return price or "价格面议"
            
        except:
            return "价格面议"

    async def _search_with_playwright(self, query: str, account_username: str = None) -> Dict:
        """使用Playwright方案搜索"""
        # 这里可以调用现有的Playwright京东爬虫
        try:
            from .platforms.jingdong import JingdongCrawler
            crawler = JingdongCrawler()
            results = await crawler._search_implementation(query, max_pages=1)
            
            products = []
            for result in results:
                products.append({
                    "title": result.title,
                    "price": result.price,
                    "image": result.image_url,
                    "link": result.product_url,
                    "platform": "jingdong",
                    "shop_name": result.shop_name or "京东自营",
                    "sales": result.sales or "评论未知",
                    "location": result.location or "全国",
                    "is_post_free": "包邮"
                })
            
            return {
                "platform": "jingdong",
                "query": query,
                "total": len(products),
                "products": products,
                "response_time": 2.0,
                "account_used": account_username,
                "crawl_method": "Playwright"
            }
            
        except Exception as e:
            print(f"❌ Playwright搜索京东失败: {str(e)}")
            return self._get_fallback_data(query, f"Playwright错误: {str(e)}", account_username)

    def _get_fallback_data(self, query: str, error_msg: str = "", account_username: str = None) -> Dict:
        """获取回退数据 - 返回错误信息而不是模拟数据"""
        return {
            "platform": "jingdong",
            "query": query,
            "total": 0,
            "products": [],
            "response_time": 0,
            "account_used": account_username or "未配置",
            "crawl_method": "爬虫失败",
            "error": error_msg or "无法获取真实商品数据"
        }


# 全局爬虫实例
_jingdong_crawler = None

async def get_jingdong_crawler():
    """获取京东爬虫实例"""
    global _jingdong_crawler
    if _jingdong_crawler is None:
        _jingdong_crawler = RealJingdongCrawler()
    return _jingdong_crawler
