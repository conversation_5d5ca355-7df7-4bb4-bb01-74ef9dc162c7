# AqentCrawler Linux CentOS7 部署指南

## 📋 目录
- [环境准备](#环境准备)
- [打包发布](#打包发布)
- [Linux部署](#linux部署)
- [环境区分](#环境区分)
- [Windows vs Linux差异](#windows-vs-linux差异)
- [生产环境优化](#生产环境优化)
- [监控和维护](#监控和维护)

## 🛠️ 环境准备

### CentOS7 系统要求
```bash
# 系统版本
cat /etc/redhat-release
# CentOS Linux release 7.x.x (Core)

# 最低配置
CPU: 2核心
内存: 4GB
存储: 20GB
网络: 稳定的互联网连接
```

### 1. 安装基础软件

#### 更新系统
```bash
sudo yum update -y
sudo yum install -y epel-release
sudo yum groupinstall -y "Development Tools"
```

#### 安装Python 3.8+
```bash
# 安装Python 3.8
sudo yum install -y python38 python38-pip python38-devel

# 创建软链接
sudo ln -sf /usr/bin/python3.8 /usr/bin/python3
sudo ln -sf /usr/bin/pip3.8 /usr/bin/pip3

# 验证安装
python3 --version
pip3 --version
```

#### 安装Node.js 16+
```bash
# 安装NodeSource仓库
curl -fsSL https://rpm.nodesource.com/setup_16.x | sudo bash -

# 安装Node.js
sudo yum install -y nodejs

# 验证安装
node --version
npm --version
```

#### 安装MySQL 8.0
```bash
# 下载MySQL仓库
wget https://dev.mysql.com/get/mysql80-community-release-el7-3.noarch.rpm
sudo rpm -ivh mysql80-community-release-el7-3.noarch.rpm

# 安装MySQL
sudo yum install -y mysql-server

# 启动MySQL服务
sudo systemctl start mysqld
sudo systemctl enable mysqld

# 获取临时密码
sudo grep 'temporary password' /var/log/mysqld.log

# 安全配置
sudo mysql_secure_installation
```

#### 安装Redis 6.0+
```bash
# 安装Redis
sudo yum install -y redis

# 启动Redis服务
sudo systemctl start redis
sudo systemctl enable redis

# 验证安装
redis-cli ping
```

#### 安装Nginx
```bash
# 安装Nginx
sudo yum install -y nginx

# 启动Nginx服务
sudo systemctl start nginx
sudo systemctl enable nginx

# 验证安装
sudo systemctl status nginx
```

#### 安装Chrome浏览器
```bash
# 添加Google Chrome仓库
sudo tee /etc/yum.repos.d/google-chrome.repo <<EOF
[google-chrome]
name=google-chrome
baseurl=http://dl.google.com/linux/chrome/rpm/stable/x86_64
enabled=1
gpgcheck=1
gpgkey=https://dl.google.com/linux/linux_signing_key.pub
EOF

# 安装Chrome
sudo yum install -y google-chrome-stable

# 安装中文字体支持
sudo yum install -y wqy-microhei-fonts wqy-zenhei-fonts
```

### 2. 创建应用用户
```bash
# 创建应用用户
sudo useradd -m -s /bin/bash aqentcrawler
sudo usermod -aG wheel aqentcrawler

# 切换到应用用户
sudo su - aqentcrawler
```

## 📦 打包发布

### 1. 在Windows开发环境打包

#### 执行打包
```bash
# 在Windows开发环境中执行
cd AqentCrawler
python scripts/package.py
```

打包完成后会生成：
- `build/aqentcrawler-YYYYMMDD-HHMMSS.zip` - 部署包
- `build/aqentcrawler-YYYYMMDD-HHMMSS/` - 解压目录

#### 打包内容
```
aqentcrawler-package/
├── backend/                # 后端代码
├── frontend/              # 前端构建文件
├── scripts/               # 脚本工具
├── chrome-extension/      # 浏览器扩展
├── docs/                  # 文档文件
├── .env.production        # 生产环境配置模板
├── deploy.sh             # Linux部署脚本
└── package.json          # 包信息
```

### 2. 上传到Linux服务器

#### 使用SCP上传
```bash
# 从Windows上传到Linux
scp aqentcrawler-YYYYMMDD-HHMMSS.zip user@server:/home/<USER>/
```

#### 使用FTP/SFTP
```bash
# 使用FileZilla或WinSCP等工具上传
# 目标路径: /home/<USER>/
```

## 🐧 Linux部署

### 1. 解压和部署

#### 切换到应用用户
```bash
sudo su - aqentcrawler
cd /home/<USER>
```

#### 解压部署包
```bash
# 解压
unzip aqentcrawler-YYYYMMDD-HHMMSS.zip
cd aqentcrawler-YYYYMMDD-HHMMSS

# 运行部署脚本
chmod +x deploy.sh
./deploy.sh
```

### 2. 配置环境

#### 编辑生产环境配置
```bash
cd /home/<USER>/aqentcrawler/backend
cp .env.production .env
vim .env
```

必须修改的配置项：
```bash
# 数据库密码
MYSQL_PASSWORD=your_secure_password

# JWT密钥（32位随机字符串）
JWT_SECRET_KEY=your-super-secret-jwt-key-32-chars

# 加密密钥（32位随机字符串）
ENCRYPTION_KEY=your-encryption-key-32-chars-long

# 管理员密码
ADMIN_PASSWORD=your_secure_admin_password
```

#### 创建数据库
```bash
# 登录MySQL
mysql -u root -p

# 创建数据库和用户
CREATE DATABASE aqentcrawler CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'aqentcrawler'@'localhost' IDENTIFIED BY 'your_secure_password';
GRANT ALL PRIVILEGES ON aqentcrawler.* TO 'aqentcrawler'@'localhost';
FLUSH PRIVILEGES;
EXIT;
```

#### 初始化数据库
```bash
cd /home/<USER>/aqentcrawler
python3 scripts/init_db.py
```

### 3. 安装Python依赖
```bash
cd /home/<USER>/aqentcrawler/backend
pip3 install --user -r requirements.txt

# 如果需要安装到系统级别
sudo pip3 install -r requirements.txt
```

### 4. 配置Nginx

#### 创建Nginx配置
```bash
sudo vim /etc/nginx/conf.d/aqentcrawler.conf
```

```nginx
server {
    listen 80;
    server_name your-domain.com;  # 替换为你的域名或IP

    # 前端静态文件
    location / {
        root /home/<USER>/aqentcrawler/frontend;
        try_files $uri $uri/ /index.html;

        # 缓存静态资源
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }

    # 后端API代理
    location /api/ {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # API文档
    location /docs {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }

    # 健康检查
    location /health {
        proxy_pass http://127.0.0.1:8000;
        access_log off;
    }
}
```

#### 测试和重载Nginx
```bash
# 测试配置
sudo nginx -t

# 重载配置
sudo systemctl reload nginx
```

### 5. 创建Systemd服务

#### 创建服务文件
```bash
sudo vim /etc/systemd/system/aqentcrawler.service
```

```ini
[Unit]
Description=AqentCrawler API Service
After=network.target mysql.service redis.service
Wants=mysql.service redis.service

[Service]
Type=exec
User=aqentcrawler
Group=aqentcrawler
WorkingDirectory=/home/<USER>/aqentcrawler/backend
Environment=PATH=/home/<USER>/.local/bin:/usr/local/bin:/usr/bin:/bin
ExecStart=/usr/bin/python3 -m gunicorn app.main:app -w 4 -k uvicorn.workers.UvicornWorker --bind 127.0.0.1:8000
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
```

#### 启动服务
```bash
# 重载systemd配置
sudo systemctl daemon-reload

# 启用服务
sudo systemctl enable aqentcrawler

# 启动服务
sudo systemctl start aqentcrawler

# 查看状态
sudo systemctl status aqentcrawler
```

## 🔄 环境区分

### 1. 环境配置文件

#### 开发环境 (.env.development)
```bash
# 开发环境配置
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=DEBUG

# 数据库（本地）
DATABASE_URL=mysql+pymysql://root:password@localhost:3306/aqentcrawler_dev

# Redis（本地）
REDIS_URL=redis://localhost:6379/1

# 服务配置
HOST=127.0.0.1
PORT=8000
WORKERS=1

# Chrome配置
CHROME_PATH=C:\Program Files\Google\Chrome\Application\chrome.exe
CHROME_USER_DATA_DIR=C:\AgentCrawler\Temp
```

#### 测试环境 (.env.testing)
```bash
# 测试环境配置
ENVIRONMENT=testing
DEBUG=false
LOG_LEVEL=INFO

# 数据库（测试服务器）
DATABASE_URL=mysql+pymysql://test_user:password@test-server:3306/aqentcrawler_test

# Redis（测试服务器）
REDIS_URL=redis://test-server:6379/2

# 服务配置
HOST=0.0.0.0
PORT=8000
WORKERS=2
```

#### 生产环境 (.env.production)
```bash
# 生产环境配置
ENVIRONMENT=production
DEBUG=false
LOG_LEVEL=WARNING

# 数据库（生产服务器）
DATABASE_URL=mysql+pymysql://prod_user:secure_password@prod-server:3306/aqentcrawler

# Redis（生产服务器）
REDIS_URL=redis://prod-server:6379/0

# 服务配置
HOST=0.0.0.0
PORT=8000
WORKERS=4

# 安全配置
JWT_SECRET_KEY=production-super-secret-key
ENCRYPTION_KEY=production-encryption-key-32-chars
```

### 2. 环境检测和切换

#### 创建环境管理脚本
```python
# scripts/env_manager.py
import os
import shutil
from pathlib import Path

class EnvironmentManager:
    def __init__(self):
        self.backend_dir = Path(__file__).parent.parent / "backend"
        self.env_files = {
            "dev": self.backend_dir / ".env.development",
            "test": self.backend_dir / ".env.testing",
            "prod": self.backend_dir / ".env.production"
        }
        self.current_env = self.backend_dir / ".env"

    def switch_env(self, env_name):
        """切换环境"""
        if env_name not in self.env_files:
            raise ValueError(f"未知环境: {env_name}")

        source_file = self.env_files[env_name]
        if not source_file.exists():
            raise FileNotFoundError(f"环境配置文件不存在: {source_file}")

        # 备份当前配置
        if self.current_env.exists():
            backup_file = self.backend_dir / f".env.backup.{int(time.time())}"
            shutil.copy2(self.current_env, backup_file)

        # 切换配置
        shutil.copy2(source_file, self.current_env)
        print(f"✅ 已切换到 {env_name} 环境")

    def get_current_env(self):
        """获取当前环境"""
        if not self.current_env.exists():
            return "未配置"

        with open(self.current_env, 'r') as f:
            content = f.read()
            if 'ENVIRONMENT=development' in content:
                return "development"
            elif 'ENVIRONMENT=testing' in content:
                return "testing"
            elif 'ENVIRONMENT=production' in content:
                return "production"
            else:
                return "未知"

if __name__ == "__main__":
    import sys
    import time

    manager = EnvironmentManager()

    if len(sys.argv) < 2:
        print(f"当前环境: {manager.get_current_env()}")
        print("使用方法: python env_manager.py [dev|test|prod]")
        sys.exit(1)

    env_name = sys.argv[1]
    try:
        manager.switch_env(env_name)
    except Exception as e:
        print(f"❌ 切换环境失败: {e}")
        sys.exit(1)
```

#### 使用环境管理
```bash
# 切换到开发环境
python scripts/env_manager.py dev

# 切换到测试环境
python scripts/env_manager.py test

# 切换到生产环境
python scripts/env_manager.py prod

# 查看当前环境
python scripts/env_manager.py
```

### 3. 启动脚本环境区分

#### 修改启动脚本
```python
# scripts/start_prod.py 添加环境检测
import os
from pathlib import Path

def check_environment():
    """检查当前环境"""
    env_file = Path(__file__).parent.parent / "backend" / ".env"

    if not env_file.exists():
        print("❌ 环境配置文件不存在")
        return False

    with open(env_file, 'r') as f:
        content = f.read()

    if 'ENVIRONMENT=production' not in content:
        print("⚠️  警告: 当前不是生产环境配置")
        response = input("是否继续? (y/N): ")
        if response.lower() != 'y':
            return False

    return True

def main():
    if not check_environment():
        sys.exit(1)

    # 继续启动流程...
```

## 🔄 Windows vs Linux差异

### 1. 路径差异

| 项目 | Windows | Linux |
|------|---------|-------|
| 路径分隔符 | `\` | `/` |
| Chrome路径 | `C:\Program Files\Google\Chrome\Application\chrome.exe` | `/usr/bin/google-chrome` |
| 用户数据目录 | `C:\AgentCrawler\Temp` | `/home/<USER>/chrome_data` |
| 日志目录 | `.\logs` | `/home/<USER>/logs` |
| 配置文件 | `.\backend\.env` | `/home/<USER>/aqentcrawler/backend/.env` |

### 2. 服务管理差异

#### Windows (开发环境)
```bash
# 直接运行
python backend/start_server.py

# 或使用uvicorn
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

#### Linux (生产环境)
```bash
# 使用systemd服务
sudo systemctl start aqentcrawler
sudo systemctl stop aqentcrawler
sudo systemctl restart aqentcrawler
sudo systemctl status aqentcrawler

# 查看日志
sudo journalctl -u aqentcrawler -f
```

### 3. 权限管理差异

#### Windows
- 通常以当前用户运行
- 较少的权限限制
- UAC控制

#### Linux
- 使用专用应用用户 (aqentcrawler)
- 严格的文件权限控制
- sudo权限管理

```bash
# 设置文件权限
chmod 755 /home/<USER>/aqentcrawler/scripts/*.py
chmod 644 /home/<USER>/aqentcrawler/backend/.env
chown -R aqentcrawler:aqentcrawler /home/<USER>/aqentcrawler
```

### 4. 依赖安装差异

#### Windows
```bash
# 使用pip直接安装
pip install -r requirements.txt

# 或使用conda
conda install package_name
```

#### Linux
```bash
# 用户级安装
pip3 install --user -r requirements.txt

# 系统级安装
sudo pip3 install -r requirements.txt

# 使用包管理器
sudo yum install python3-package
```

### 5. 网络配置差异

#### Windows
- 通常使用127.0.0.1或localhost
- 防火墙配置相对简单
- 代理配置通过系统设置

#### Linux
- 需要配置iptables或firewalld
- 更复杂的网络安全配置
- 代理配置通过环境变量

```bash
# Linux防火墙配置
sudo firewall-cmd --permanent --add-port=80/tcp
sudo firewall-cmd --permanent --add-port=8000/tcp
sudo firewall-cmd --reload

# 或使用iptables
sudo iptables -A INPUT -p tcp --dport 80 -j ACCEPT
sudo iptables -A INPUT -p tcp --dport 8000 -j ACCEPT
```

## 🚀 生产环境优化

### 1. 性能优化

#### Gunicorn配置优化
```bash
# 创建 gunicorn.conf.py
bind = "127.0.0.1:8000"
workers = 4  # CPU核心数 * 2
worker_class = "uvicorn.workers.UvicornWorker"
worker_connections = 1000
max_requests = 1000
max_requests_jitter = 100
timeout = 30
keepalive = 2
preload_app = True

# 日志配置
accesslog = "/home/<USER>/logs/access.log"
errorlog = "/home/<USER>/logs/error.log"
loglevel = "info"
access_log_format = '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s" %(D)s'
```

#### 启动命令
```bash
gunicorn -c gunicorn.conf.py app.main:app
```

### 2. 数据库优化

#### MySQL配置优化
```bash
# 编辑 /etc/my.cnf
[mysqld]
# 连接配置
max_connections = 200
max_connect_errors = 10000

# 缓存配置
innodb_buffer_pool_size = 1G
query_cache_size = 256M
query_cache_type = 1

# 日志配置
slow_query_log = 1
slow_query_log_file = /var/log/mysql/slow.log
long_query_time = 2

# 字符集
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci
```

#### Redis配置优化
```bash
# 编辑 /etc/redis.conf
maxmemory 512mb
maxmemory-policy allkeys-lru
save 900 1
save 300 10
save 60 10000
```

### 3. 系统资源优化

#### 文件描述符限制
```bash
# 编辑 /etc/security/limits.conf
aqentcrawler soft nofile 65536
aqentcrawler hard nofile 65536

# 编辑 /etc/systemd/system/aqentcrawler.service
[Service]
LimitNOFILE=65536
```

#### 内核参数优化
```bash
# 编辑 /etc/sysctl.conf
net.core.somaxconn = 65535
net.ipv4.tcp_max_syn_backlog = 65535
net.core.netdev_max_backlog = 5000
vm.swappiness = 10
```

### 4. 安全配置

#### SSL/TLS配置
```nginx
server {
    listen 443 ssl http2;
    server_name your-domain.com;

    ssl_certificate /etc/ssl/certs/your-domain.crt;
    ssl_certificate_key /etc/ssl/private/your-domain.key;

    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;

    # 其他配置...
}

# HTTP重定向到HTTPS
server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}
```

#### 访问控制
```nginx
# 限制API访问频率
location /api/ {
    limit_req zone=api burst=10 nodelay;
    proxy_pass http://127.0.0.1:8000;
}

# 定义限制区域
http {
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
}
```

## 📊 监控和维护

### 1. 日志管理

#### 日志轮转配置
```bash
# 创建 /etc/logrotate.d/aqentcrawler
/home/<USER>/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 aqentcrawler aqentcrawler
    postrotate
        systemctl reload aqentcrawler
    endscript
}
```

#### 日志监控脚本
```bash
#!/bin/bash
# scripts/monitor_logs.sh

LOG_DIR="/home/<USER>/logs"
ERROR_THRESHOLD=10

# 检查错误日志
error_count=$(grep -c "ERROR" $LOG_DIR/error.log)
if [ $error_count -gt $ERROR_THRESHOLD ]; then
    echo "警告: 错误日志数量过多 ($error_count)"
    # 发送告警邮件或通知
fi

# 检查磁盘空间
disk_usage=$(df /home/<USER>'NR==2 {print $5}' | sed 's/%//')
if [ $disk_usage -gt 80 ]; then
    echo "警告: 磁盘空间不足 ($disk_usage%)"
fi
```

### 2. 健康检查

#### 创建健康检查脚本
```bash
#!/bin/bash
# scripts/health_check.sh

API_URL="http://localhost:8000/health"
TIMEOUT=10

# 检查API服务
response=$(curl -s -w "%{http_code}" -o /dev/null --max-time $TIMEOUT $API_URL)

if [ $response -eq 200 ]; then
    echo "✅ API服务正常"
else
    echo "❌ API服务异常 (HTTP: $response)"
    # 重启服务
    sudo systemctl restart aqentcrawler
fi

# 检查数据库连接
mysql -u aqentcrawler -p$MYSQL_PASSWORD -e "SELECT 1" aqentcrawler > /dev/null 2>&1
if [ $? -eq 0 ]; then
    echo "✅ 数据库连接正常"
else
    echo "❌ 数据库连接异常"
fi

# 检查Redis连接
redis-cli ping > /dev/null 2>&1
if [ $? -eq 0 ]; then
    echo "✅ Redis连接正常"
else
    echo "❌ Redis连接异常"
fi
```

### 3. 定时任务

#### 设置crontab
```bash
# 编辑定时任务
crontab -e

# 添加以下任务
# 每5分钟检查服务健康状态
*/5 * * * * /home/<USER>/aqentcrawler/scripts/health_check.sh >> /home/<USER>/logs/health.log 2>&1

# 每小时清理临时文件
0 * * * * find /home/<USER>/chrome_data -name "*.tmp" -mtime +1 -delete

# 每天备份数据库
0 2 * * * mysqldump -u aqentcrawler -p$MYSQL_PASSWORD aqentcrawler > /home/<USER>/backup/db_$(date +\%Y\%m\%d).sql

# 每周清理旧日志
0 0 * * 0 find /home/<USER>/logs -name "*.log.*" -mtime +7 -delete
```

### 4. 性能监控

#### 系统资源监控
```bash
#!/bin/bash
# scripts/system_monitor.sh

# CPU使用率
cpu_usage=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | sed 's/%us,//')

# 内存使用率
mem_usage=$(free | grep Mem | awk '{printf "%.2f", $3/$2 * 100.0}')

# 磁盘使用率
disk_usage=$(df -h /home/<USER>'NR==2 {print $5}' | sed 's/%//')

echo "$(date): CPU: ${cpu_usage}%, MEM: ${mem_usage}%, DISK: ${disk_usage}%"

# 记录到监控日志
echo "$(date),${cpu_usage},${mem_usage},${disk_usage}" >> /home/<USER>/logs/system_monitor.log
```

### 5. 备份策略

#### 数据库备份脚本
```bash
#!/bin/bash
# scripts/backup_database.sh

BACKUP_DIR="/home/<USER>/backup"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="$BACKUP_DIR/aqentcrawler_$DATE.sql"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份数据库
mysqldump -u aqentcrawler -p$MYSQL_PASSWORD \
    --single-transaction \
    --routines \
    --triggers \
    aqentcrawler > $BACKUP_FILE

# 压缩备份文件
gzip $BACKUP_FILE

# 删除7天前的备份
find $BACKUP_DIR -name "*.sql.gz" -mtime +7 -delete

echo "数据库备份完成: $BACKUP_FILE.gz"
```

## 🔧 故障排除

### 1. 常见问题

#### 服务启动失败
```bash
# 查看服务状态
sudo systemctl status aqentcrawler

# 查看详细日志
sudo journalctl -u aqentcrawler -f

# 检查配置文件
python3 -c "from app.database import test_database_connection; test_database_connection()"
```

#### 数据库连接问题
```bash
# 检查MySQL服务
sudo systemctl status mysqld

# 测试连接
mysql -u aqentcrawler -p -h localhost aqentcrawler

# 检查防火墙
sudo firewall-cmd --list-ports
```

#### Chrome启动问题
```bash
# 检查Chrome安装
google-chrome --version

# 测试Chrome启动
google-chrome --headless --no-sandbox --disable-gpu --dump-dom https://www.baidu.com

# 检查字体支持
fc-list | grep -i chinese
```

### 2. 性能问题排查

#### 查看系统资源
```bash
# CPU和内存使用
top -p $(pgrep -f aqentcrawler)

# 磁盘IO
iotop -p $(pgrep -f aqentcrawler)

# 网络连接
netstat -tulpn | grep :8000
```

#### 数据库性能
```bash
# 查看慢查询
mysql -u root -p -e "SHOW VARIABLES LIKE 'slow_query_log';"
mysql -u root -p -e "SHOW STATUS LIKE 'Slow_queries';"

# 查看连接数
mysql -u root -p -e "SHOW STATUS LIKE 'Threads_connected';"
```

---

**版本**: v1.0.0
**更新时间**: 2025-01-20
**适用系统**: CentOS 7.x