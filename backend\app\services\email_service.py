"""
邮件服务模块
提供邮件发送功能，支持HTML模板和附件
"""

import os
import asyncio
import aiosmtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.mime.base import MIMEBase
from email import encoders
from email.utils import formataddr
from typing import List, Optional, Dict, Any
from pathlib import Path
import logging
from jinja2 import Environment, FileSystemLoader, Template
from datetime import datetime

logger = logging.getLogger(__name__)

class EmailService:
    """邮件服务类"""
    
    def __init__(self):
        """初始化邮件服务"""
        try:
            self.smtp_server = os.getenv('SMTP_SERVER', 'smtp.qq.com')
            self.smtp_port = int(os.getenv('SMTP_PORT', '587'))
            self.smtp_username = os.getenv('SMTP_USERNAME', '')
            self.smtp_password = os.getenv('SMTP_PASSWORD', '')
            self.smtp_use_tls = os.getenv('SMTP_USE_TLS', 'true').lower() == 'true'
            self.smtp_use_ssl = os.getenv('SMTP_USE_SSL', 'false').lower() == 'true'

            # 发件人信息
            self.sender_email = os.getenv('SENDER_EMAIL', self.smtp_username)
            self.sender_name = os.getenv('SENDER_NAME', 'AqentCrawler系统')

            # 模板目录
            self.template_dir = Path(__file__).parent.parent / 'templates' / 'email'
            self.template_dir.mkdir(parents=True, exist_ok=True)

            # 初始化Jinja2环境
            self.jinja_env = Environment(
                loader=FileSystemLoader(str(self.template_dir)),
                autoescape=True
            )

            # 验证配置
            self.is_configured = self._validate_config()

        except Exception as e:
            logger.warning(f"邮件服务初始化失败: {e}")
            self.is_configured = False
    
    def _validate_config(self):
        """验证邮件配置"""
        if not self.smtp_username or not self.smtp_password:
            logger.warning("邮件配置不完整，邮件功能将不可用")
            return False
        
        if not self.sender_email:
            self.sender_email = self.smtp_username
        
        return True
    
    async def send_email(
        self,
        to_emails: List[str],
        subject: str,
        content: str,
        content_type: str = 'html',
        cc_emails: Optional[List[str]] = None,
        bcc_emails: Optional[List[str]] = None,
        attachments: Optional[List[Dict[str, Any]]] = None,
        reply_to: Optional[str] = None
    ) -> bool:
        """
        发送邮件
        
        Args:
            to_emails: 收件人邮箱列表
            subject: 邮件主题
            content: 邮件内容
            content_type: 内容类型 ('html' 或 'plain')
            cc_emails: 抄送邮箱列表
            bcc_emails: 密送邮箱列表
            attachments: 附件列表 [{'filename': str, 'content': bytes, 'content_type': str}]
            reply_to: 回复邮箱
        
        Returns:
            bool: 发送是否成功
        """
        try:
            if not self.is_configured:
                logger.warning("邮件服务未配置，无法发送邮件")
                return False
            
            # 创建邮件消息
            msg = MIMEMultipart()
            msg['From'] = formataddr((self.sender_name, self.sender_email))
            msg['To'] = ', '.join(to_emails)
            msg['Subject'] = subject
            
            if cc_emails:
                msg['Cc'] = ', '.join(cc_emails)
            
            if reply_to:
                msg['Reply-To'] = reply_to
            
            # 添加邮件内容
            if content_type.lower() == 'html':
                msg.attach(MIMEText(content, 'html', 'utf-8'))
            else:
                msg.attach(MIMEText(content, 'plain', 'utf-8'))
            
            # 添加附件
            if attachments:
                for attachment in attachments:
                    part = MIMEBase('application', 'octet-stream')
                    part.set_payload(attachment['content'])
                    encoders.encode_base64(part)
                    part.add_header(
                        'Content-Disposition',
                        f'attachment; filename= {attachment["filename"]}'
                    )
                    msg.attach(part)
            
            # 准备收件人列表
            recipients = to_emails.copy()
            if cc_emails:
                recipients.extend(cc_emails)
            if bcc_emails:
                recipients.extend(bcc_emails)
            
            # 发送邮件
            await self._send_smtp(msg, recipients)
            
            logger.info(f"邮件发送成功: {subject} -> {', '.join(to_emails)}")
            return True
            
        except Exception as e:
            logger.error(f"邮件发送失败: {e}")
            return False
    
    async def _send_smtp(self, msg: MIMEMultipart, recipients: List[str]):
        """通过SMTP发送邮件"""
        if self.smtp_use_ssl:
            # 使用SSL连接
            smtp = aiosmtplib.SMTP(
                hostname=self.smtp_server,
                port=self.smtp_port,
                use_tls=False,
                start_tls=False
            )
        else:
            # 使用TLS连接
            smtp = aiosmtplib.SMTP(
                hostname=self.smtp_server,
                port=self.smtp_port,
                use_tls=self.smtp_use_tls,
                start_tls=self.smtp_use_tls
            )
        
        await smtp.connect()
        
        if self.smtp_username and self.smtp_password:
            await smtp.login(self.smtp_username, self.smtp_password)
        
        await smtp.send_message(msg, recipients=recipients)
        await smtp.quit()
    
    async def send_template_email(
        self,
        to_emails: List[str],
        template_name: str,
        subject: str,
        template_data: Dict[str, Any],
        **kwargs
    ) -> bool:
        """
        使用模板发送邮件
        
        Args:
            to_emails: 收件人邮箱列表
            template_name: 模板名称 (不含扩展名)
            subject: 邮件主题
            template_data: 模板数据
            **kwargs: 其他邮件参数
        
        Returns:
            bool: 发送是否成功
        """
        try:
            # 加载模板
            template = self.jinja_env.get_template(f'{template_name}.html')
            
            # 渲染模板
            content = template.render(**template_data)
            
            # 发送邮件
            return await self.send_email(
                to_emails=to_emails,
                subject=subject,
                content=content,
                content_type='html',
                **kwargs
            )
            
        except Exception as e:
            logger.error(f"模板邮件发送失败: {e}")
            return False
    
    def create_template(self, template_name: str, template_content: str):
        """
        创建邮件模板
        
        Args:
            template_name: 模板名称
            template_content: 模板内容 (HTML)
        """
        template_path = self.template_dir / f'{template_name}.html'
        with open(template_path, 'w', encoding='utf-8') as f:
            f.write(template_content)
        
        logger.info(f"邮件模板已创建: {template_path}")
    
    async def test_connection(self) -> bool:
        """
        测试邮件服务器连接
        
        Returns:
            bool: 连接是否成功
        """
        try:
            if not self.is_configured:
                logger.warning("邮件服务未配置，无法测试连接")
                return False
            
            if self.smtp_use_ssl:
                smtp = aiosmtplib.SMTP(
                    hostname=self.smtp_server,
                    port=self.smtp_port,
                    use_tls=False,
                    start_tls=False
                )
            else:
                smtp = aiosmtplib.SMTP(
                    hostname=self.smtp_server,
                    port=self.smtp_port,
                    use_tls=self.smtp_use_tls,
                    start_tls=self.smtp_use_tls
                )
            
            await smtp.connect()
            
            if self.smtp_username and self.smtp_password:
                await smtp.login(self.smtp_username, self.smtp_password)
            
            await smtp.quit()
            
            logger.info("邮件服务器连接测试成功")
            return True
            
        except Exception as e:
            logger.error(f"邮件服务器连接测试失败: {e}")
            return False

# 全局邮件服务实例
try:
    email_service = EmailService()
except Exception as e:
    logger.error(f"邮件服务初始化失败: {e}")
    # 创建一个空的邮件服务实例
    class DummyEmailService:
        def __init__(self):
            self.is_configured = False

        async def send_email(self, *args, **kwargs):
            logger.warning("邮件服务未配置，无法发送邮件")
            return False

        async def send_template_email(self, *args, **kwargs):
            logger.warning("邮件服务未配置，无法发送模板邮件")
            return False

        async def test_connection(self):
            logger.warning("邮件服务未配置，无法测试连接")
            return False

    email_service = DummyEmailService()
