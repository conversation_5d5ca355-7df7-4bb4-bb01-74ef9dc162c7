#!/usr/bin/env python3
"""
生产环境启动脚本
"""
import os
import sys
import subprocess
import time
import signal
import json
from pathlib import Path

# 项目根目录
PROJECT_ROOT = Path(__file__).parent.parent
BACKEND_DIR = PROJECT_ROOT / "backend"
FRONTEND_DIR = PROJECT_ROOT / "frontend"

class ProdServer:
    def __init__(self):
        self.backend_process = None
        self.running = True
        
    def check_environment(self):
        """检查生产环境"""
        print("🔍 检查生产环境...")
        
        # 检查环境变量
        required_env = [
            "JWT_SECRET_KEY",
            "MYSQL_HOST",
            "MYSQL_USER", 
            "MYSQL_PASSWORD",
            "MYSQL_DATABASE"
        ]
        
        missing_env = []
        for env_var in required_env:
            if not os.getenv(env_var):
                missing_env.append(env_var)
        
        if missing_env:
            print(f"❌ 缺少环境变量: {', '.join(missing_env)}")
            print("💡 请设置必要的环境变量或创建 .env 文件")
            return False
        
        # 检查前端构建文件
        dist_dir = FRONTEND_DIR / "dist"
        if not dist_dir.exists():
            print("❌ 前端构建文件不存在")
            print("💡 请先运行: npm run build")
            return False
        
        print("✅ 生产环境检查通过")
        return True
    
    def build_frontend(self):
        """构建前端"""
        print("🏗️  构建前端...")
        
        try:
            # 安装依赖
            print("📦 安装前端依赖...")
            npm_cmd = "npm.cmd" if os.name == 'nt' else "npm"
            result = subprocess.run([
                npm_cmd, "install", "--production"
            ], cwd=FRONTEND_DIR, check=True)

            # 构建
            print("🔨 构建前端...")
            result = subprocess.run([
                npm_cmd, "run", "build"
            ], cwd=FRONTEND_DIR, check=True)
            
            print("✅ 前端构建完成")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"❌ 前端构建失败: {e}")
            return False
    
    def start_backend(self):
        """启动后端服务"""
        print("🚀 启动生产后端服务...")
        
        try:
            # 设置环境变量
            env = os.environ.copy()
            env.update({
                "PYTHONPATH": str(BACKEND_DIR),
                "ENVIRONMENT": "production"
            })
            
            # 生产环境配置
            workers = os.getenv("WORKERS", "4")
            host = os.getenv("HOST", "0.0.0.0")
            port = os.getenv("PORT", "8000")
            
            # 启动gunicorn服务器
            self.backend_process = subprocess.Popen([
                "gunicorn",
                "app.main:app",
                "-w", workers,
                "-k", "uvicorn.workers.UvicornWorker",
                "--bind", f"{host}:{port}",
                "--access-logfile", "-",
                "--error-logfile", "-",
                "--log-level", "info",
                "--timeout", "120",
                "--keep-alive", "5"
            ], cwd=BACKEND_DIR, env=env)
            
            print(f"✅ 后端服务启动成功 (http://{host}:{port})")
            return True
            
        except Exception as e:
            print(f"❌ 后端服务启动失败: {e}")
            return False
    
    def setup_nginx(self):
        """生成Nginx配置"""
        print("📝 生成Nginx配置...")
        
        nginx_config = f"""
server {{
    listen 80;
    server_name localhost;
    
    # 前端静态文件
    location / {{
        root {FRONTEND_DIR}/dist;
        try_files $uri $uri/ /index.html;
        
        # 缓存静态资源
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {{
            expires 1y;
            add_header Cache-Control "public, immutable";
        }}
    }}
    
    # API代理
    location /api/ {{
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }}
    
    # 健康检查
    location /health {{
        proxy_pass http://127.0.0.1:8000;
        access_log off;
    }}
}}
"""
        
        config_file = PROJECT_ROOT / "nginx.conf"
        with open(config_file, "w", encoding="utf-8") as f:
            f.write(nginx_config.strip())
        
        print(f"✅ Nginx配置已生成: {config_file}")
        print("💡 请将配置复制到Nginx配置目录")
    
    def create_systemd_service(self):
        """生成systemd服务文件"""
        print("📝 生成systemd服务文件...")
        
        service_config = f"""[Unit]
Description=AqentCrawler API Service
After=network.target

[Service]
Type=simple
User=www-data
Group=www-data
WorkingDirectory={BACKEND_DIR}
Environment=PYTHONPATH={BACKEND_DIR}
Environment=ENVIRONMENT=production
ExecStart=/usr/bin/python3 {PROJECT_ROOT}/scripts/start_prod.py
Restart=always
RestartSec=5

[Install]
WantedBy=multi-user.target
"""
        
        service_file = PROJECT_ROOT / "aqentcrawler.service"
        with open(service_file, "w", encoding="utf-8") as f:
            f.write(service_config.strip())
        
        print(f"✅ systemd服务文件已生成: {service_file}")
        print("💡 安装命令:")
        print(f"   sudo cp {service_file} /etc/systemd/system/")
        print("   sudo systemctl daemon-reload")
        print("   sudo systemctl enable aqentcrawler")
        print("   sudo systemctl start aqentcrawler")
    
    def signal_handler(self, signum, frame):
        """信号处理器"""
        print("\n🛑 收到停止信号，正在关闭服务...")
        self.running = False
        self.stop_services()
        sys.exit(0)
    
    def stop_services(self):
        """停止服务"""
        if self.backend_process:
            print("🛑 停止后端服务...")
            self.backend_process.terminate()
            try:
                self.backend_process.wait(timeout=10)
            except subprocess.TimeoutExpired:
                self.backend_process.kill()
    
    def run(self, build_frontend=False, generate_configs=False):
        """运行生产服务器"""
        print("🎯 AqentCrawler 生产环境启动")
        print("=" * 50)
        
        # 注册信号处理器
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
        
        try:
            # 检查环境
            if not self.check_environment():
                return False
            
            # 构建前端
            if build_frontend:
                if not self.build_frontend():
                    return False
            
            # 生成配置文件
            if generate_configs:
                self.setup_nginx()
                self.create_systemd_service()
                print("\n✅ 配置文件生成完成")
                return True
            
            # 启动后端
            if not self.start_backend():
                return False
            
            print("\n🎉 生产环境启动完成!")
            print("📋 服务信息:")
            print(f"   API服务: http://0.0.0.0:8000")
            print("   进程数: 4个worker")
            print("\n💡 使用说明:")
            print("   - 建议使用Nginx作为反向代理")
            print("   - 建议使用systemd管理服务")
            print("   - 按 Ctrl+C 停止服务")
            print("\n⏳ 服务运行中...")
            
            # 保持运行
            while self.running:
                if self.backend_process.poll() is not None:
                    print("❌ 后端服务异常退出")
                    break
                time.sleep(5)
                
        except KeyboardInterrupt:
            print("\n🛑 用户中断，正在停止服务...")
        except Exception as e:
            print(f"❌ 启动失败: {e}")
        finally:
            self.stop_services()
            print("✅ 服务已停止")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="AqentCrawler 生产环境启动脚本")
    parser.add_argument("--build", action="store_true", help="构建前端")
    parser.add_argument("--config", action="store_true", help="生成配置文件")
    parser.add_argument("--help-deploy", action="store_true", help="显示部署帮助")
    
    args = parser.parse_args()
    
    if args.help_deploy:
        print("🚀 AqentCrawler 生产环境部署指南")
        print("\n1. 环境准备:")
        print("   - Python 3.8+")
        print("   - Node.js 16+")
        print("   - MySQL 8.0+")
        print("   - Redis 6.0+")
        print("   - Nginx (可选)")
        print("\n2. 安装依赖:")
        print("   pip install -r backend/requirements.txt")
        print("   pip install gunicorn")
        print("\n3. 配置环境变量:")
        print("   export JWT_SECRET_KEY='your-secret-key'")
        print("   export MYSQL_HOST='localhost'")
        print("   export MYSQL_USER='root'")
        print("   export MYSQL_PASSWORD='password'")
        print("   export MYSQL_DATABASE='aqentcrawler'")
        print("\n4. 构建和启动:")
        print("   python scripts/start_prod.py --build")
        print("   python scripts/start_prod.py")
        print("\n5. 生成配置文件:")
        print("   python scripts/start_prod.py --config")
        return
    
    server = ProdServer()
    
    if args.config:
        server.run(generate_configs=True)
    else:
        server.run(build_frontend=args.build)

if __name__ == "__main__":
    main()
