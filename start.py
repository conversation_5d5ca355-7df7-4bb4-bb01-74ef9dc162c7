#!/usr/bin/env python3
"""
AqentCrawler 快速启动脚本
"""
import os
import sys
import subprocess
from pathlib import Path

def main():
    """主函数"""
    print("🎯 AqentCrawler 快速启动")
    print("=" * 40)
    
    # 检查Python版本
    if sys.version_info < (3, 8):
        print("❌ 需要Python 3.8或更高版本")
        sys.exit(1)
    
    # 项目根目录
    project_root = Path(__file__).parent
    
    # 显示选项
    print("请选择启动模式:")
    print("1. 开发环境 (自动重载，调试模式)")
    print("2. 生产环境 (性能优化，多进程)")
    print("3. 仅启动后端API")
    print("4. 仅启动前端")
    print("5. 安装依赖")
    print("6. 显示帮助")
    print("0. 退出")
    
    while True:
        try:
            choice = input("\n请输入选择 (0-6): ").strip()
            
            if choice == "0":
                print("👋 再见!")
                break
            elif choice == "1":
                # 开发环境
                script_path = project_root / "scripts" / "start_dev.py"
                subprocess.run([sys.executable, str(script_path)])
                break
            elif choice == "2":
                # 生产环境
                script_path = project_root / "scripts" / "start_prod.py"
                subprocess.run([sys.executable, str(script_path)])
                break
            elif choice == "3":
                # 仅后端
                backend_dir = project_root / "backend"
                print("🚀 启动后端API服务...")
                subprocess.run([
                    sys.executable, "-m", "uvicorn",
                    "app.main:app",
                    "--reload",
                    "--host", "0.0.0.0",
                    "--port", "8000"
                ], cwd=backend_dir)
                break
            elif choice == "4":
                # 仅前端
                frontend_dir = project_root / "frontend"
                print("🚀 启动前端开发服务器...")
                npm_cmd = "npm.cmd" if os.name == 'nt' else "npm"
                subprocess.run([npm_cmd, "run", "dev"], cwd=frontend_dir)
                break
            elif choice == "5":
                # 安装依赖
                install_dependencies(project_root)
                break
            elif choice == "6":
                # 显示帮助
                show_help()
                break
            else:
                print("❌ 无效选择，请输入0-6")
        except KeyboardInterrupt:
            print("\n👋 再见!")
            break
        except Exception as e:
            print(f"❌ 错误: {e}")

def install_dependencies(project_root):
    """安装依赖"""
    print("📦 安装项目依赖...")
    
    # 安装后端依赖
    backend_dir = project_root / "backend"
    requirements_file = backend_dir / "requirements.txt"
    
    if requirements_file.exists():
        print("📦 安装Python依赖...")
        try:
            subprocess.run([
                sys.executable, "-m", "pip", "install", "-r", str(requirements_file)
            ], check=True)
            print("✅ Python依赖安装完成")
        except subprocess.CalledProcessError:
            print("❌ Python依赖安装失败")
            return
    
    # 安装前端依赖
    frontend_dir = project_root / "frontend"
    package_json = frontend_dir / "package.json"
    
    if package_json.exists():
        print("📦 安装Node.js依赖...")
        try:
            npm_cmd = "npm.cmd" if os.name == 'nt' else "npm"
            subprocess.run([npm_cmd, "install"], cwd=frontend_dir, check=True)
            print("✅ Node.js依赖安装完成")
        except subprocess.CalledProcessError:
            print("❌ Node.js依赖安装失败")
            return
    
    print("🎉 所有依赖安装完成!")

def show_help():
    """显示帮助信息"""
    print("\n📖 AqentCrawler 使用指南")
    print("=" * 40)
    
    print("\n🚀 快速开始:")
    print("1. 首次使用请先选择 '5. 安装依赖'")
    print("2. 开发时选择 '1. 开发环境'")
    print("3. 部署时选择 '2. 生产环境'")
    
    print("\n🔧 环境配置:")
    print("- 开发环境: 自动使用内置配置")
    print("- 生产环境: 需要配置环境变量")
    
    print("\n📋 默认端口:")
    print("- 后端API: http://localhost:8000")
    print("- 前端界面: http://localhost:3000")
    print("- API文档: http://localhost:8000/docs")
    
    print("\n🔑 默认账号:")
    print("- 用户名: admin")
    print("- 密码: admin123")
    
    print("\n📚 更多信息:")
    print("- 开发文档: README.md")
    print("- API文档: backend/API_DOCUMENTATION.md")
    
    print("\n💡 常用命令:")
    print("- 开发环境: python scripts/start_dev.py")
    print("- 生产环境: python scripts/start_prod.py")
    print("- 仅后端: cd backend && uvicorn app.main:app --reload")
    print("- 仅前端: cd frontend && npm run dev")

if __name__ == "__main__":
    main()
