/**
 * 淘宝配置提取器 - 弹窗脚本
 */

class TaobaoExtractorPopup {
    constructor() {
        this.config = null;
        this.backendUrl = 'http://localhost:8000';
        this.init();
    }

    async init() {
        // 绑定事件
        this.bindEvents();
        
        // 检查当前标签页
        await this.checkCurrentTab();
        
        // 加载设置
        await this.loadSettings();
    }

    bindEvents() {
        document.getElementById('extractBtn').addEventListener('click', () => {
            this.extractConfig();
        });

        document.getElementById('settingsBtn').addEventListener('click', () => {
            this.openSettings();
        });
    }

    async checkCurrentTab() {
        try {
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            
            if (!tab.url.includes('taobao.com') && !tab.url.includes('tmall.com')) {
                this.updateStatus('warning', '请在淘宝或天猫页面使用此扩展');
                return;
            }

            // 检查页面状态
            await this.checkPageStatus(tab.id);
            
        } catch (error) {
            console.error('检查标签页失败:', error);
            this.updateStatus('error', '检查页面状态失败');
        }
    }

    async checkPageStatus(tabId) {
        try {
            // 向content script发送消息检查状态
            const response = await chrome.tabs.sendMessage(tabId, { 
                action: 'checkStatus' 
            });

            if (response && response.success) {
                this.updateAccountInfo(response.data);
                this.updateStatus('success', '页面检测成功，可以提取配置');
                document.getElementById('extractBtn').disabled = false;
            } else {
                this.updateStatus('warning', '页面未完全加载，请刷新后重试');
            }
        } catch (error) {
            console.error('检查页面状态失败:', error);
            this.updateStatus('warning', '无法连接到页面，请刷新后重试');
        }
    }

    updateAccountInfo(data) {
        const accountInfoDiv = document.getElementById('accountInfo');
        accountInfoDiv.style.display = 'block';

        document.getElementById('username').textContent = data.username || '未检测到';
        document.getElementById('loginStatus').textContent = data.isLoggedIn ? '✅ 已登录' : '❌ 未登录';
        document.getElementById('tokenStatus').textContent = data.hasToken ? '✅ Token可用' : '❌ Token不可用';
    }

    updateStatus(type, message) {
        const statusDiv = document.getElementById('status');
        statusDiv.className = `status ${type}`;
        statusDiv.textContent = message;
    }

    async extractConfig() {
        const extractBtn = document.getElementById('extractBtn');
        const extractText = document.getElementById('extractText');
        
        // 显示加载状态
        extractBtn.disabled = true;
        extractText.innerHTML = '<span class="loading"></span>提取中...';

        try {
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            
            // 向content script发送提取请求
            const response = await chrome.tabs.sendMessage(tab.id, { 
                action: 'extractConfig' 
            });

            if (response && response.success) {
                this.config = response.data;
                this.showConfigPreview();
                await this.sendToBackend();
                this.updateStatus('success', '✅ 配置提取并发送成功！');
            } else {
                throw new Error(response?.error || '提取配置失败');
            }

        } catch (error) {
            console.error('提取配置失败:', error);
            this.updateStatus('error', `❌ ${error.message}`);
        } finally {
            // 恢复按钮状态
            extractBtn.disabled = false;
            extractText.textContent = '提取配置';
        }
    }

    showConfigPreview() {
        if (!this.config) return;

        const previewDiv = document.getElementById('configPreview');
        previewDiv.style.display = 'block';

        document.getElementById('cookiePreview').textContent = 
            this.config.cookie ? this.config.cookie.substring(0, 100) + '...' : '未获取到';
        
        document.getElementById('tokenPreview').textContent = 
            this.config.token || '未获取到';
        
        document.getElementById('userAgentPreview').textContent = 
            this.config.userAgent ? this.config.userAgent.substring(0, 50) + '...' : '未获取到';
    }

    async sendToBackend() {
        if (!this.config) {
            throw new Error('没有配置数据可发送');
        }

        try {
            const response = await fetch(`${this.backendUrl}/api/v1/config/update`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    platform: 'taobao',
                    username: this.config.username,
                    cookie: this.config.cookie,
                    token: this.config.token,
                    user_agent: this.config.userAgent,
                    extracted_at: new Date().toISOString()
                })
            });

            if (!response.ok) {
                throw new Error(`后端响应错误: ${response.status}`);
            }

            const result = await response.json();
            if (result.code !== 200) {
                throw new Error(result.message || '后端处理失败');
            }

            console.log('配置发送成功:', result);
            
            // 保存到本地存储
            await this.saveToStorage();

        } catch (error) {
            console.error('发送到后端失败:', error);
            // 即使发送失败，也保存到本地
            await this.saveToStorage();
            throw new Error(`发送到后端失败: ${error.message}`);
        }
    }

    async saveToStorage() {
        if (!this.config) return;

        try {
            await chrome.storage.local.set({
                lastConfig: this.config,
                lastExtractTime: Date.now()
            });
            console.log('配置已保存到本地存储');
        } catch (error) {
            console.error('保存到本地存储失败:', error);
        }
    }

    async loadSettings() {
        try {
            const result = await chrome.storage.sync.get(['backendUrl']);
            if (result.backendUrl) {
                this.backendUrl = result.backendUrl;
            }
        } catch (error) {
            console.error('加载设置失败:', error);
        }
    }

    openSettings() {
        // 打开设置页面
        chrome.tabs.create({
            url: chrome.runtime.getURL('settings.html')
        });
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    new TaobaoExtractorPopup();
});
