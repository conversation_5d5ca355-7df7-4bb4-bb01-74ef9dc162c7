/**
 * 淘宝配置提取器 - 设置页面脚本
 */

class SettingsManager {
    constructor() {
        this.init();
    }

    async init() {
        // 加载当前设置
        await this.loadSettings();
        
        // 绑定事件
        this.bindEvents();
        
        // 检查后端连接状态
        await this.checkBackendStatus();
        
        // 加载配置历史
        await this.loadConfigHistory();
    }

    bindEvents() {
        // 保存设置
        document.getElementById('settingsForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveSettings();
        });

        // 重置设置
        document.getElementById('resetBtn').addEventListener('click', () => {
            this.resetSettings();
        });

        // 测试连接
        document.getElementById('testConnection').addEventListener('click', () => {
            this.testBackendConnection();
        });

        // 后端地址变化时自动测试
        document.getElementById('backendUrl').addEventListener('blur', () => {
            this.checkBackendStatus();
        });
    }

    async loadSettings() {
        try {
            const settings = await chrome.storage.sync.get([
                'backendUrl',
                'autoExtract',
                'extractInterval',
                'notificationEnabled'
            ]);

            document.getElementById('backendUrl').value = settings.backendUrl || 'http://localhost:8000';
            document.getElementById('autoExtract').checked = settings.autoExtract || false;
            document.getElementById('extractInterval').value = (settings.extractInterval || 3600000) / 60000; // 转换为分钟
            document.getElementById('notificationEnabled').checked = settings.notificationEnabled !== false;

            console.log('设置已加载:', settings);
        } catch (error) {
            console.error('加载设置失败:', error);
            this.showStatus('error', '加载设置失败');
        }
    }

    async saveSettings() {
        try {
            const settings = {
                backendUrl: document.getElementById('backendUrl').value.trim(),
                autoExtract: document.getElementById('autoExtract').checked,
                extractInterval: parseInt(document.getElementById('extractInterval').value) * 60000, // 转换为毫秒
                notificationEnabled: document.getElementById('notificationEnabled').checked
            };

            // 验证设置
            if (!settings.backendUrl) {
                throw new Error('后端服务地址不能为空');
            }

            if (settings.extractInterval < 300000) { // 最少5分钟
                throw new Error('自动提取间隔不能少于5分钟');
            }

            // 保存设置
            await chrome.storage.sync.set(settings);

            console.log('设置已保存:', settings);
            this.showStatus('success', '设置保存成功！');

            // 重新检查后端状态
            setTimeout(() => {
                this.checkBackendStatus();
            }, 1000);

        } catch (error) {
            console.error('保存设置失败:', error);
            this.showStatus('error', `保存失败: ${error.message}`);
        }
    }

    async resetSettings() {
        if (confirm('确定要重置所有设置吗？')) {
            try {
                // 清除同步存储中的设置
                await chrome.storage.sync.clear();
                
                // 重新加载默认设置
                await this.loadSettings();
                
                this.showStatus('success', '设置已重置为默认值');
                
            } catch (error) {
                console.error('重置设置失败:', error);
                this.showStatus('error', '重置设置失败');
            }
        }
    }

    async checkBackendStatus() {
        const backendUrl = document.getElementById('backendUrl').value.trim();
        const statusIndicator = document.getElementById('statusIndicator');
        const statusText = document.getElementById('statusText');

        if (!backendUrl) {
            statusIndicator.className = 'status-indicator offline';
            statusText.textContent = '请输入后端地址';
            return;
        }

        statusText.textContent = '检测中...';

        try {
            const response = await fetch(`${backendUrl}/api/v1/health`, {
                method: 'GET',
                timeout: 5000
            });

            if (response.ok) {
                statusIndicator.className = 'status-indicator online';
                statusText.textContent = '连接正常';
            } else {
                statusIndicator.className = 'status-indicator offline';
                statusText.textContent = `连接失败 (${response.status})`;
            }

        } catch (error) {
            statusIndicator.className = 'status-indicator offline';
            statusText.textContent = '连接失败';
            console.error('检查后端状态失败:', error);
        }
    }

    async testBackendConnection() {
        const testBtn = document.getElementById('testConnection');
        const originalText = testBtn.textContent;
        
        testBtn.textContent = '测试中...';
        testBtn.disabled = true;

        try {
            await this.checkBackendStatus();
            
            // 尝试发送测试请求
            const backendUrl = document.getElementById('backendUrl').value.trim();
            const response = await fetch(`${backendUrl}/api/v1/config/test`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    test: true,
                    timestamp: Date.now()
                })
            });

            if (response.ok) {
                this.showStatus('success', '后端连接测试成功！');
            } else {
                this.showStatus('error', `连接测试失败: HTTP ${response.status}`);
            }

        } catch (error) {
            console.error('测试连接失败:', error);
            this.showStatus('error', `连接测试失败: ${error.message}`);
        } finally {
            testBtn.textContent = originalText;
            testBtn.disabled = false;
        }
    }

    async loadConfigHistory() {
        try {
            const result = await chrome.storage.local.get(null);
            const configs = [];

            // 查找所有配置记录
            for (const [key, value] of Object.entries(result)) {
                if (key.startsWith('config_') && value.username) {
                    configs.push(value);
                }
            }

            // 按时间排序
            configs.sort((a, b) => (b.savedAt || 0) - (a.savedAt || 0));

            // 显示最近的5条记录
            this.displayConfigHistory(configs.slice(0, 5));

        } catch (error) {
            console.error('加载配置历史失败:', error);
        }
    }

    displayConfigHistory(configs) {
        const historyDiv = document.getElementById('configHistory');
        
        if (configs.length === 0) {
            historyDiv.innerHTML = '<div class="config-item"><div class="time">暂无历史记录</div></div>';
            return;
        }

        historyDiv.innerHTML = configs.map(config => {
            const time = config.savedAt ? new Date(config.savedAt).toLocaleString() : '未知时间';
            const username = config.username || '未知用户';
            const hasToken = config.token ? '✅' : '❌';
            
            return `
                <div class="config-item">
                    <div class="time">${time}</div>
                    <div>用户: <span class="username">${username}</span></div>
                    <div>Token: ${hasToken} | Cookie: ${config.cookie ? '✅' : '❌'}</div>
                </div>
            `;
        }).join('');
    }

    showStatus(type, message) {
        const statusDiv = document.getElementById('status');
        statusDiv.className = `status ${type}`;
        statusDiv.textContent = message;
        statusDiv.style.display = 'block';

        // 3秒后自动隐藏
        setTimeout(() => {
            statusDiv.style.display = 'none';
        }, 3000);
    }

    // 导出配置
    async exportConfig() {
        try {
            const result = await chrome.storage.local.get(['lastConfig']);
            if (result.lastConfig) {
                const dataStr = JSON.stringify(result.lastConfig, null, 2);
                const dataBlob = new Blob([dataStr], { type: 'application/json' });
                
                const url = URL.createObjectURL(dataBlob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `taobao-config-${Date.now()}.json`;
                a.click();
                
                URL.revokeObjectURL(url);
                this.showStatus('success', '配置导出成功！');
            } else {
                this.showStatus('error', '没有可导出的配置');
            }
        } catch (error) {
            console.error('导出配置失败:', error);
            this.showStatus('error', '导出配置失败');
        }
    }

    // 清除历史记录
    async clearHistory() {
        if (confirm('确定要清除所有配置历史记录吗？')) {
            try {
                const result = await chrome.storage.local.get(null);
                const keysToRemove = [];

                for (const key of Object.keys(result)) {
                    if (key.startsWith('config_')) {
                        keysToRemove.push(key);
                    }
                }

                if (keysToRemove.length > 0) {
                    await chrome.storage.local.remove(keysToRemove);
                    await this.loadConfigHistory();
                    this.showStatus('success', '历史记录已清除');
                } else {
                    this.showStatus('error', '没有历史记录可清除');
                }

            } catch (error) {
                console.error('清除历史记录失败:', error);
                this.showStatus('error', '清除历史记录失败');
            }
        }
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    new SettingsManager();
});
