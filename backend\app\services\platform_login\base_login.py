"""
基础平台登录类

定义所有平台登录服务的通用接口和基础功能（不包含Token刷新）
"""

import re
from abc import ABC, abstractmethod
from datetime import datetime, timedelta
from typing import Dict, Optional, Tuple

from playwright.sync_api import Page

from app.models import CrawlerAccount
from app.utils.crypto import decrypt_password


class BasePlatformLogin(ABC):
    """平台登录基础类"""

    def __init__(self, platform_code: str):
        self.platform_code = platform_code
        self.login_timeout = 300  # 5分钟登录超时
        self.check_interval = 2   # 2秒检查一次登录状态

    @abstractmethod
    def get_login_url(self) -> str:
        """获取登录页面URL"""
        pass

    @abstractmethod
    def get_login_selectors(self) -> Dict[str, str]:
        """获取登录页面选择器"""
        pass

    @abstractmethod
    def get_success_indicators(self) -> list:
        """获取登录成功的URL指示器"""
        pass

    @abstractmethod
    def get_verification_indicators(self) -> list:
        """获取验证过程的URL指示器"""
        pass

    @abstractmethod
    def extract_token_from_cookies(self, cookies: list) -> Tuple[Optional[str], Optional[datetime]]:
        """从Cookie中提取Token和过期时间"""
        pass

    def setup_browser_context(self, context):
        """设置浏览器上下文（反检测等）"""
        # 应用反检测脚本
        context.add_init_script("""
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined,
            });
            
            window.chrome = {
                runtime: {},
                loadTimes: function() {},
                csi: function() {},
                app: {}
            };
            
            Object.defineProperty(navigator, 'plugins', {
                get: () => [1, 2, 3, 4, 5],
            });
            
            Object.defineProperty(navigator, 'languages', {
                get: () => ['zh-CN', 'zh', 'en'],
            });
        """)

    def prefill_username(self, page: Page, account: CrawlerAccount) -> bool:
        """预填用户名"""
        selectors = self.get_login_selectors()
        username_selector = selectors.get('username')

        if username_selector:
            try:
                print(f"预填用户名: {account.username}")
                page.fill(username_selector, account.username)
                page.wait_for_timeout(1000)
                return True
            except Exception as e:
                print(f"预填用户名失败: {e}")

        return False

    def prefill_password(self, page: Page, account: CrawlerAccount) -> bool:
        """预填密码"""
        if not account.password:
            print("未设置密码，跳过密码预填")
            return False

        selectors = self.get_login_selectors()
        password_selector = selectors.get('password')

        if password_selector:
            try:
                # 解密密码
                decrypted_password = decrypt_password(account.password)
                if decrypted_password:
                    print("预填密码...")
                    page.fill(password_selector, decrypted_password)
                    page.wait_for_timeout(1000)
                    print("密码预填成功")
                    return True
                else:
                    print("密码解密失败，跳过密码预填")
            except Exception as e:
                print(f"预填密码失败: {e}")
        else:
            print("未找到密码输入框选择器，跳过密码预填")

        return False

    def prefill_credentials(self, page: Page, account: CrawlerAccount) -> Dict[str, bool]:
        """预填用户名和密码"""
        username_filled = self.prefill_username(page, account)
        password_filled = self.prefill_password(page, account)

        return {
            "username_filled": username_filled,
            "password_filled": password_filled
        }

    def wait_for_login_completion(self, page: Page) -> Dict[str, any]:
        """等待登录完成"""
        login_success = False
        elapsed_time = 0
        verification_detected = False

        print("开始检测登录状态...")
        print(f"系统将每{self.check_interval}秒检测一次登录状态，最多等待{self.login_timeout}秒...")

        success_indicators = self.get_success_indicators()
        verification_indicators = self.get_verification_indicators()

        while elapsed_time < self.login_timeout and not login_success:
            try:
                current_url = page.url
                print(f"检查登录状态: {current_url} (已等待{elapsed_time}秒)")

                # 检测是否在验证过程中
                is_in_verification = any(indicator in current_url.lower() for indicator in verification_indicators)

                # 检查成功页面
                is_success_page = any(indicator in current_url.lower() for indicator in success_indicators)

                if is_success_page and not is_in_verification:
                    # 检查是否有有效的Token
                    try:
                        cookies = page.context.cookies()
                        token, token_expires_at = self.extract_token_from_cookies(cookies)

                        if token:
                            print(f"检测到登录成功页面，Token: {token[:10]}...")
                            if token_expires_at:
                                print(f"Token过期时间: {token_expires_at}")
                            login_success = True
                            break
                        else:
                            print("检测到登录成功页面，但未获取到有效Token，继续等待...")
                    except Exception as e:
                        print(f"检查Token时出错: {str(e)}")

                elif is_in_verification:
                    if not verification_detected:
                        print("检测到验证过程，请完成验证...")
                        verification_detected = True
                else:
                    # 检查是否有登录错误
                    try:
                        error_elements = page.query_selector_all(".error, .login-error, [class*='error']")
                        if error_elements:
                            error_text = error_elements[0].text_content()
                            if error_text and "密码" in error_text:
                                return {"success": False, "message": f"登录失败: {error_text}"}
                    except:
                        pass

                page.wait_for_timeout(self.check_interval * 1000)
                elapsed_time += self.check_interval

            except Exception as e:
                print(f"检查登录状态时出错: {str(e)}")
                page.wait_for_timeout(self.check_interval * 1000)
                elapsed_time += self.check_interval

        if not login_success:
            return {"success": False, "message": f"登录超时（{self.login_timeout}秒），请检查是否完成了所有验证步骤或获取到有效Token"}

        return {"success": True, "message": "登录成功"}

    def extract_cookies_and_token(self, page: Page) -> Dict[str, any]:
        """提取Cookie和Token"""
        try:
            # 获取所有Cookie
            cookies = page.context.cookies()
            cookie_dict = {cookie['name']: cookie['value'] for cookie in cookies}

            # 提取Token
            token, token_expires_at = self.extract_token_from_cookies(cookies)

            # 格式化cookie字符串
            cookie_string = "; ".join([f"{name}={value}" for name, value in cookie_dict.items()])

            # 获取User-Agent
            user_agent = page.evaluate("navigator.userAgent")

            print(f"提取到 {len(cookie_dict)} 个Cookie")
            print(f"Token: {token[:10] if token else 'None'}...")

            return {
                "success": True,
                "cookie": cookie_string,
                "token": token,
                "token_expires_at": token_expires_at,
                "user_agent": user_agent,
                "cookie_count": len(cookie_dict)
            }

        except Exception as e:
            print(f"提取Cookie和Token失败: {str(e)}")
            return {"success": False, "message": f"提取Cookie和Token失败: {str(e)}"}

    def login(self, page: Page, account: CrawlerAccount) -> Dict[str, any]:
        """执行登录流程"""
        try:
            # 1. 访问登录页面
            login_url = self.get_login_url()
            print(f"访问{self.platform_code}登录页面...")
            
            try:
                page.goto(login_url, timeout=60000)
                print("页面加载完成，等待DOM加载...")
                page.wait_for_load_state("domcontentloaded", timeout=30000)
                print("DOM加载完成，等待3秒...")
                page.wait_for_timeout(3000)
                print("页面加载完成，继续执行...")
            except Exception as e:
                print(f"页面加载失败: {str(e)}")
                return {"success": False, "message": f"页面加载失败: {str(e)}"}

            # 2. 预填用户名和密码
            prefill_result = self.prefill_credentials(page, account)
            if prefill_result["username_filled"]:
                print("用户名预填成功")
            if prefill_result["password_filled"]:
                print("密码预填成功")
            elif account.password:
                print("密码预填失败，请手动输入密码")

            # 3. 显示登录提示
            print(f"请在浏览器中手动完成{self.platform_code}登录操作：")
            print("1. 输入用户名和密码")
            print("2. 完成验证码、滑块等验证")
            print("3. 完成手机验证码验证（如果需要）")
            print("4. 点击登录按钮")
            print("重要：如果需要手机验证码，请耐心等待，系统会等待您完成整个验证过程")

            # 4. 等待登录完成
            login_result = self.wait_for_login_completion(page)
            if not login_result["success"]:
                return login_result

            # 5. 提取Cookie和Token
            extract_result = self.extract_cookies_and_token(page)
            if not extract_result["success"]:
                return extract_result

            return {
                "success": True,
                "message": f"{self.platform_code}登录成功",
                "data": {
                    "token": extract_result["token"],
                    "token_expires_at": extract_result["token_expires_at"].isoformat() if extract_result["token_expires_at"] else None,
                    "cookie": extract_result["cookie"],
                    "user_agent": extract_result["user_agent"],
                    "cookie_count": extract_result["cookie_count"]
                }
            }

        except Exception as e:
            print(f"{self.platform_code}登录失败: {str(e)}")
            return {"success": False, "message": f"{self.platform_code}登录失败: {str(e)}"}
