# 监控功能问题修复报告

## 问题描述

用户反馈监控功能显示"发现 1 个账号超过7天未使用"，但实际上该账号刚刚登录成功并使用了一次。

## 问题分析

经过代码分析，发现了以下问题：

### 1. 数据表不一致问题

**问题根源**：系统中存在两个账号表，但使用不一致：

- **PlatformAccount表**：主搜索API (`app/main.py`) 使用此表查询账号
- **CrawlerAccount表**：监控系统和淘宝爬虫使用此表进行统计

**影响**：
- 主搜索API使用PlatformAccount表的账号，但不更新CrawlerAccount表的使用统计
- 监控系统检查CrawlerAccount表的`last_used_at`字段，发现为NULL或过期
- 导致监控误报"账号超过7天未使用"

### 2. 账号使用统计更新缺失

**问题**：
- 主搜索API没有调用账号使用统计更新方法
- 部分爬虫平台（如京东）没有集成账号使用统计
- `last_used_at`字段没有正确更新

## 修复方案

### 1. 统一账号表使用

**修改文件**：`backend/app/main.py`

**修改内容**：
```python
# 修改前：使用PlatformAccount表
from .models import PlatformAccount, Platform
account = db.query(PlatformAccount).join(Platform).filter(
    Platform.code == platform,
    PlatformAccount.status == "active"
).first()

# 修改后：使用CrawlerAccount表
from .models import CrawlerAccount, Platform
from sqlalchemy import and_

platform_obj = db.query(Platform).filter(Platform.code == platform).first()
account = db.query(CrawlerAccount).filter(
    and_(
        CrawlerAccount.platform_id == platform_obj.id,
        CrawlerAccount.status == "active",
        CrawlerAccount.login_status == "logged_in"
    )
).first()

# 添加使用统计更新
if account:
    from app.services.config_service import get_config_service
    config_service = get_config_service(db)
    config_service._update_account_usage(account)
```

### 2. 完善淘宝爬虫统计更新

**修改文件**：`backend/app/crawler/platforms/taobao_correct.py`

**修改内容**：
```python
# 在选择账号后立即更新使用统计
from app.services.config_service import get_config_service
config_service = get_config_service(db)
config_service._update_account_usage(account)
print(f"📊 已更新账号使用统计: {account.username}")
```

### 3. 修复数据库字段定义

**问题**：`login_status`字段的ENUM定义不包含`pending`值

**解决方案**：
```sql
ALTER TABLE crawler_accounts 
MODIFY COLUMN login_status ENUM(
    'not_logged_in', 
    'pending', 
    'logging_in',
    'logged_in', 
    'login_failed', 
    'expired',
    'error'
) DEFAULT 'not_logged_in' COMMENT '登录状态';
```

### 4. 改进服务异常处理

**修改文件**：`scripts/start_dev.py`

**改进内容**：
- 添加服务自动重启机制
- 设置最大重启次数限制
- 前端服务失败不导致整个系统停止

## 监控系统原理总结

### 核心组件

1. **AccountMonitorService**
   - 主监控服务，运行在独立异步任务中
   - 监控间隔：60秒
   - 健康检查间隔：300秒

2. **监控功能**
   - Token过期监控（提前2小时警告）
   - 自动Token刷新（提前1小时）
   - 账号健康检查
   - 数据清理

3. **统计字段**
   - `last_used_at`：最后使用时间
   - `total_requests`：总请求数
   - `success_requests`：成功请求数
   - `success_rate`：成功率
   - `error_count`：连续错误次数

### 监控逻辑

#### 长时间未使用检查
```python
# 检查超过7天未使用的账号
inactive_threshold = datetime.now() - timedelta(days=7)
inactive_accounts = db.query(CrawlerAccount).filter(
    or_(
        CrawlerAccount.last_used_at.is_(None),  # 从未使用
        CrawlerAccount.last_used_at < inactive_threshold  # 超过7天未使用
    )
).count()
```

#### 账号使用统计更新
```python
def _update_account_usage(self, account: CrawlerAccount):
    """更新账号使用统计"""
    account.last_used_at = datetime.now()
    account.current_requests_count = (account.current_requests_count or 0) + 1
    account.total_requests = (account.total_requests or 0) + 1
    self.db.commit()
```

### 状态定义

#### 登录状态 (login_status)
- `not_logged_in`：未登录
- `pending`：等待登录
- `logging_in`：登录中
- `logged_in`：已登录
- `login_failed`：登录失败
- `expired`：Token已过期
- `error`：错误状态

#### 账号状态 (status)
- `active`：活跃
- `inactive`：非活跃
- `suspended`：暂停
- `error`：错误

## 验证步骤

### 1. 数据库修复
```sql
-- 执行字段修复SQL
ALTER TABLE crawler_accounts 
MODIFY COLUMN login_status ENUM(
    'not_logged_in', 'pending', 'logging_in',
    'logged_in', 'login_failed', 'expired', 'error'
) DEFAULT 'not_logged_in';
```

### 2. 重启服务
```bash
python start.py
# 选择 1 (开发环境)
```

### 3. 测试搜索API
```bash
curl -X POST http://localhost:8000/api/v1/search \
  -H "Content-Type: application/json" \
  -d '{"platform": "taobao", "query": "手机"}'
```

### 4. 检查监控状态
```bash
curl http://localhost:8000/api/v1/crawler/monitor/status
```

### 5. 验证账号使用统计
```sql
SELECT username, last_used_at, total_requests, success_requests 
FROM crawler_accounts 
ORDER BY last_used_at DESC;
```

## 预期结果

修复后应该实现：

1. ✅ 搜索API调用时正确更新`last_used_at`字段
2. ✅ 监控系统不再误报"7天未使用"
3. ✅ 账号使用统计准确记录
4. ✅ 服务异常时自动重启，不会整体停止
5. ✅ 数据库字段支持所有必要的状态值

## 后续优化建议

1. **统一数据模型**：考虑合并PlatformAccount和CrawlerAccount表
2. **监控仪表板**：开发前端监控界面显示实时状态
3. **告警机制**：添加邮件/短信告警功能
4. **性能优化**：优化监控查询性能
5. **日志增强**：添加更详细的监控日志

---

*修复完成时间：2025-06-15*  
*修复人员：Augment Agent*
