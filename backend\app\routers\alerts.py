"""
告警管理路由
提供告警配置、状态查看、手动测试等功能
"""

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import Dict, Any, List
from pydantic import BaseModel, EmailStr
import logging

from app.database import get_db
from app.services.alert_service import alert_service, AlertLevel, AlertType
from app.services.health_check_service import health_check_service
from app.utils.response import success_response, error_response

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/v1/alerts", tags=["告警管理"])

# ==================== 请求模型 ====================

class TestAlertRequest(BaseModel):
    """测试告警请求"""
    alert_type: str
    title: str
    message: str
    level: str = "warning"

class AlertConfigRequest(BaseModel):
    """告警配置请求"""
    enabled: bool
    recipients: List[EmailStr]
    cooldown_minutes: int = 30

# ==================== API接口 ====================

@router.get("/status")
async def get_alert_status():
    """获取告警服务状态"""
    try:
        status = alert_service.get_alert_status()
        health_status = {
            'running': health_check_service.running,
            'check_interval': health_check_service.check_interval
        }
        
        return success_response({
            'alert_service': status,
            'health_check_service': health_status
        })
        
    except Exception as e:
        logger.error(f"获取告警状态失败: {e}")
        return error_response(f"获取告警状态失败: {str(e)}")

@router.post("/test")
async def test_alert(request: TestAlertRequest):
    """测试发送告警"""
    try:
        # 验证告警级别
        try:
            level = AlertLevel(request.level)
        except ValueError:
            return error_response(f"无效的告警级别: {request.level}")
        
        # 验证告警类型
        try:
            alert_type = AlertType(request.alert_type)
        except ValueError:
            return error_response(f"无效的告警类型: {request.alert_type}")
        
        # 根据告警类型发送测试告警
        success = False
        
        if alert_type == AlertType.SYSTEM:
            success = await alert_service.send_system_alert(
                title=request.title,
                message=request.message,
                level=level,
                details={'测试': '这是一个测试告警'},
                metrics=[
                    {'name': '测试指标', 'value': '100', 'unit': '%'}
                ],
                recommendations=['这是测试建议']
            )
        elif alert_type == AlertType.CRAWLER:
            success = await alert_service.send_crawler_alert(
                title=request.title,
                message=request.message,
                level=level,
                crawler_accounts=[{
                    'platform': 'test',
                    'platform_name': '测试平台',
                    'username': 'test_user',
                    'status': 'test',
                    'status_text': '测试状态',
                    'status_class': 'info',
                    'updated_at': '2024-01-01 12:00:00',
                    'remark': '这是测试账号'
                }],
                statistics=[
                    {'name': '测试统计', 'value': '1', 'unit': '个'}
                ],
                next_actions=['这是测试操作建议']
            )
        elif alert_type == AlertType.PROXY:
            success = await alert_service.send_proxy_alert(
                title=request.title,
                message=request.message,
                level=level,
                proxy_info=[{
                    'host': '127.0.0.1',
                    'port': 8080,
                    'status': 'active',
                    'response_time': 100,
                    'last_check': '2024-01-01 12:00:00'
                }],
                statistics=[
                    {'name': '测试代理数', 'value': '1', 'unit': '个'}
                ]
            )
        elif alert_type == AlertType.API:
            success = await alert_service.send_api_alert(
                title=request.title,
                message=request.message,
                level=level,
                api_info={
                    '接口': 'test_api',
                    '平台': 'test',
                    '状态': '测试'
                },
                error_details={
                    '错误信息': '这是测试错误',
                    '发生时间': '2024-01-01 12:00:00'
                }
            )
        
        if success:
            return success_response({'sent': True, 'message': '测试告警发送成功'})
        else:
            return error_response('测试告警发送失败')
            
    except Exception as e:
        logger.error(f"测试告警失败: {e}")
        return error_response(f"测试告警失败: {str(e)}")

@router.post("/clear-cache")
async def clear_alert_cache():
    """清理告警缓存"""
    try:
        alert_service.clear_alert_cache()
        return success_response({'message': '告警缓存已清理'})
        
    except Exception as e:
        logger.error(f"清理告警缓存失败: {e}")
        return error_response(f"清理告警缓存失败: {str(e)}")

@router.post("/health-check/manual")
async def manual_health_check():
    """手动执行健康检查"""
    try:
        await health_check_service.perform_health_check()
        return success_response({'message': '健康检查执行完成'})
        
    except Exception as e:
        logger.error(f"手动健康检查失败: {e}")
        return error_response(f"手动健康检查失败: {str(e)}")

@router.post("/health-check/start")
async def start_health_check():
    """启动健康检查服务"""
    try:
        if health_check_service.running:
            return error_response('健康检查服务已在运行中')
        
        import asyncio
        asyncio.create_task(health_check_service.start_health_check())
        
        return success_response({'message': '健康检查服务已启动'})
        
    except Exception as e:
        logger.error(f"启动健康检查服务失败: {e}")
        return error_response(f"启动健康检查服务失败: {str(e)}")

@router.post("/health-check/stop")
async def stop_health_check():
    """停止健康检查服务"""
    try:
        if not health_check_service.running:
            return error_response('健康检查服务未在运行')
        
        health_check_service.stop_health_check()
        
        return success_response({'message': '健康检查服务已停止'})
        
    except Exception as e:
        logger.error(f"停止健康检查服务失败: {e}")
        return error_response(f"停止健康检查服务失败: {str(e)}")

@router.get("/types")
async def get_alert_types():
    """获取支持的告警类型和级别"""
    try:
        types = [{'value': t.value, 'label': t.value} for t in AlertType]
        levels = [{'value': l.value, 'label': l.name} for l in AlertLevel]
        
        return success_response({
            'types': types,
            'levels': levels
        })
        
    except Exception as e:
        logger.error(f"获取告警类型失败: {e}")
        return error_response(f"获取告警类型失败: {str(e)}")

@router.get("/config")
async def get_alert_config():
    """获取当前告警配置"""
    try:
        import os
        
        config = {
            'enabled': os.getenv('EMAIL_ALERTS_ENABLED', 'false').lower() == 'true',
            'recipients': os.getenv('ALERT_EMAIL_RECIPIENTS', '').split(',') if os.getenv('ALERT_EMAIL_RECIPIENTS') else [],
            'cooldown_minutes': int(os.getenv('ALERT_COOLDOWN_MINUTES', '30')),
            'smtp_configured': bool(os.getenv('SMTP_USERNAME') and os.getenv('SMTP_PASSWORD'))
        }
        
        return success_response(config)
        
    except Exception as e:
        logger.error(f"获取告警配置失败: {e}")
        return error_response(f"获取告警配置失败: {str(e)}")

@router.get("/statistics")
async def get_alert_statistics():
    """获取告警统计信息"""
    try:
        # 这里可以从数据库或缓存中获取告警统计信息
        # 目前返回基本信息
        stats = {
            'cached_alerts': len(alert_service.alert_cache),
            'alert_cache_keys': list(alert_service.alert_cache.keys()),
            'service_status': {
                'alert_service_enabled': alert_service.enabled,
                'health_check_running': health_check_service.running,
                'email_service_configured': getattr(alert_service, 'email_service_configured', False)
            }
        }
        
        return success_response(stats)
        
    except Exception as e:
        logger.error(f"获取告警统计失败: {e}")
        return error_response(f"获取告警统计失败: {str(e)}")
