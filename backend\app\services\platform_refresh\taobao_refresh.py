"""
淘宝Token刷新服务

专门处理淘宝平台的Token刷新功能
"""

import re
from datetime import datetime, timedelta
from typing import Dict, Optional, Tuple

from .base_refresh import BasePlatformRefresh


class TaobaoRefresh(BasePlatformRefresh):
    """淘宝Token刷新服务"""

    def __init__(self):
        super().__init__("taobao")

    def get_homepage_url(self) -> str:
        """获取淘宝主页URL"""
        return "https://www.taobao.com"
        # return "https://login.taobao.com/member/login.jhtml"

    def extract_token_from_cookies(self, cookies: list) -> Tuple[Optional[str], Optional[datetime]]:
        """从淘宝Cookie中提取Token和过期时间"""
        token = None
        token_expires_at = None

        for cookie in cookies:
            if cookie['name'] == '_m_h5_tk':
                m_h5_tk = cookie['value']
                if '_' in m_h5_tk:
                    token_parts = m_h5_tk.split('_')
                    token = token_parts[0]
                    
                    # 提取过期时间
                    if len(token_parts) > 1:
                        try:
                            expires_timestamp = int(token_parts[1]) / 1000
                            token_expires_at = datetime.fromtimestamp(expires_timestamp)
                        except (ValueError, IndexError):
                            # 如果无法解析过期时间，设置为24小时后过期
                            token_expires_at = datetime.now() + timedelta(hours=24)
                    break

        return token, token_expires_at

    def get_critical_cookies(self) -> list:
        """获取淘宝刷新时的关键Cookie列表（基于实际刷新获取的Cookie格式）"""
        return [
            'thw', 't', 'xlly_s', 'wk_cookie2', 'wk_unb', '_hvn_lgc_',
            'havana_lgc2_0', 'aui', 'mtop_partitioned_detect', '_m_h5_tk',
            '_m_h5_tk_enc', '3PcFlag', 'cna', '_samesite_flag_', 'cookie2',
            '_tb_token_', 'unb', 'sn', 'uc3', 'csg', 'lgc', 'cancelledSubSites',
            'cookie17', 'dnk', 'skt', 'existShop', 'uc4', 'tracknick', '_cc_',
            '_l_g_', 'sg', '_nk_', 'cookie1', 'sgcookie', 'havana_lgc_exp',
            'fastSlient', 'uc1', 'sdkSilent', 'havana_sdkSilent', 'sca',
            'bxuab', 'tfstk', 'isg'
        ]

    def filter_cookies(self, cookie_string: str) -> str:
        """过滤淘宝刷新时不必要的Cookie（专门针对刷新场景）"""
        if not cookie_string:
            return cookie_string

        try:
            # 分割Cookie
            cookies = cookie_string.split(';')
            critical_cookies = self.get_critical_cookies()

            # 只保留刷新时的关键Cookie
            filtered_cookies = []
            for cookie in cookies:
                cookie = cookie.strip()
                if '=' in cookie:
                    name = cookie.split('=')[0].strip()
                    if name in critical_cookies:
                        filtered_cookies.append(cookie)

            # 如果没有获取到任何关键Cookie，返回原始Cookie字符串
            if not filtered_cookies:
                print("警告: 刷新时未获取到任何关键Cookie，返回原始Cookie")
                return cookie_string

            result = '; '.join(filtered_cookies)
            print(f"淘宝刷新Cookie过滤: {len(cookies)} -> {len(filtered_cookies)} 个关键Cookie")

            # 验证是否包含必要的认证Cookie
            essential_cookies = ['_m_h5_tk', 'cookie2', '_tb_token_', 'unb']
            missing_essential = []
            for essential in essential_cookies:
                if essential not in result:
                    missing_essential.append(essential)

            if missing_essential:
                print(f"警告: 缺少关键认证Cookie: {missing_essential}")
            else:
                print("刷新Cookie包含所有必要的认证信息")

            return result

        except Exception as e:
            print(f"过滤淘宝刷新Cookie失败: {str(e)}，返回原始Cookie")
            return cookie_string
