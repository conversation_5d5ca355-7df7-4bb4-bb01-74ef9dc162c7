{% extends "base.html" %}

{% block title %}系统告警通知 - AqentCrawler{% endblock %}

{% block header_title %}系统告警{% endblock %}
{% block header_subtitle %}AqentCrawler 系统监控告警{% endblock %}

{% block content %}
<div class="alert alert-{{ alert_type or 'warning' }}">
    <h3 style="margin: 0 0 10px 0;">
        {% if alert_type == 'danger' %}🚨{% elif alert_type == 'warning' %}⚠️{% else %}ℹ️{% endif %}
        {{ alert_title or '系统告警' }}
    </h3>
    <p style="margin: 0;">{{ alert_message }}</p>
</div>

{% if alert_details %}
<h4>告警详情</h4>
<table class="data-table">
    {% for key, value in alert_details.items() %}
    <tr>
        <td style="font-weight: 600; width: 30%;">{{ key }}</td>
        <td>{{ value }}</td>
    </tr>
    {% endfor %}
</table>
{% endif %}

{% if affected_services %}
<h4>受影响的服务</h4>
<ul>
    {% for service in affected_services %}
    <li>
        <span class="status-badge status-{{ service.status or 'error' }}">
            {{ service.name }}
        </span>
        {% if service.description %}
        - {{ service.description }}
        {% endif %}
    </li>
    {% endfor %}
</ul>
{% endif %}

{% if metrics %}
<h4>系统指标</h4>
<table class="data-table">
    <thead>
        <tr>
            <th>指标名称</th>
            <th>当前值</th>
            <th>阈值</th>
            <th>状态</th>
        </tr>
    </thead>
    <tbody>
        {% for metric in metrics %}
        <tr>
            <td>{{ metric.name }}</td>
            <td>{{ metric.current_value }}</td>
            <td>{{ metric.threshold }}</td>
            <td>
                <span class="status-badge status-{{ metric.status or 'error' }}">
                    {{ metric.status_text or metric.status }}
                </span>
            </td>
        </tr>
        {% endfor %}
    </tbody>
</table>
{% endif %}

{% if recommendations %}
<h4>建议操作</h4>
<ol>
    {% for recommendation in recommendations %}
    <li>{{ recommendation }}</li>
    {% endfor %}
</ol>
{% endif %}

{% if action_url %}
<div style="text-align: center; margin: 30px 0;">
    <a href="{{ action_url }}" class="btn btn-primary">查看系统状态</a>
</div>
{% endif %}

<div class="divider"></div>

<p style="font-size: 14px; color: #666;">
    <strong>告警时间:</strong> {{ alert_time or now().strftime('%Y-%m-%d %H:%M:%S') }}<br>
    <strong>告警级别:</strong> 
    <span class="status-badge status-{{ alert_type or 'warning' }}">
        {% if alert_type == 'danger' %}严重{% elif alert_type == 'warning' %}警告{% else %}信息{% endif %}
    </span><br>
    {% if alert_id %}
    <strong>告警ID:</strong> {{ alert_id }}
    {% endif %}
</p>
{% endblock %}
