"""
创建爬虫配置表
简化版本，只包含必要的功能
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.database import engine
from sqlalchemy import text

def create_crawler_tables():
    """创建爬虫相关表"""
    
    connection = engine.connect()
    trans = connection.begin()
    
    try:
        print("🔧 开始创建爬虫配置表...")
        
        # 1. 创建爬虫账号配置表
        create_crawler_accounts_table = """
        CREATE TABLE IF NOT EXISTS crawler_accounts (
            id BIGINT PRIMARY KEY AUTO_INCREMENT,
            platform_id BIGINT NOT NULL,
            username VARCHAR(100) NOT NULL COMMENT '账号用户名',
            display_name VARCHAR(100) COMMENT '显示名称',
            cookie TEXT NOT NULL COMMENT 'Cookie信息',
            token VARCHAR(500) COMMENT 'Token信息',
            user_agent VARCHAR(1000) COMMENT 'User-Agent',
            status ENUM('active', 'inactive', 'expired', 'error') DEFAULT 'active' COMMENT '账号状态',
            priority INT DEFAULT 1 COMMENT '优先级(1-10)',
            max_requests_per_hour INT DEFAULT 100 COMMENT '每小时最大请求数',
            current_requests_count INT DEFAULT 0 COMMENT '当前小时请求计数',
            success_rate DECIMAL(5,2) DEFAULT 100.00 COMMENT '成功率百分比',
            total_requests INT DEFAULT 0 COMMENT '总请求数',
            success_requests INT DEFAULT 0 COMMENT '成功请求数',
            error_count INT DEFAULT 0 COMMENT '连续错误次数',
            last_error_message TEXT COMMENT '最后错误信息',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            last_used_at TIMESTAMP NULL COMMENT '最后使用时间',
            extracted_from VARCHAR(500) COMMENT '提取来源页面',
            notes TEXT COMMENT '备注信息',
            INDEX idx_platform_status (platform_id, status),
            INDEX idx_last_used (last_used_at),
            INDEX idx_priority (priority DESC),
            UNIQUE KEY uk_platform_username (platform_id, username),
            FOREIGN KEY (platform_id) REFERENCES platforms(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='爬虫账号配置表';
        """
        
        connection.execute(text(create_crawler_accounts_table))
        print("✅ 创建 crawler_accounts 表")
        
        # 2. 创建配置使用日志表（简化版）
        create_usage_log_table = """
        CREATE TABLE IF NOT EXISTS crawler_usage_log (
            id BIGINT PRIMARY KEY AUTO_INCREMENT,
            account_id BIGINT NOT NULL,
            platform VARCHAR(50) COMMENT '平台名称',
            search_query VARCHAR(500) COMMENT '搜索关键词',
            status ENUM('success', 'failed', 'timeout') COMMENT '使用状态',
            response_time_ms INT COMMENT '响应时间(毫秒)',
            error_message TEXT COMMENT '错误信息',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_account_id (account_id),
            INDEX idx_created_at (created_at),
            FOREIGN KEY (account_id) REFERENCES crawler_accounts(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='爬虫使用日志表';
        """
        
        connection.execute(text(create_usage_log_table))
        print("✅ 创建 crawler_usage_log 表")
        
        # 3. 迁移现有数据（如果存在）
        try:
            # 检查是否存在旧的 platform_accounts 表
            result = connection.execute(text("SHOW TABLES LIKE 'platform_accounts'"))
            if result.fetchone():
                print("🔄 检测到现有的 platform_accounts 表，开始数据迁移...")
                
                # 迁移数据到新表
                migrate_data_sql = """
                INSERT IGNORE INTO crawler_accounts (
                    platform_id, username, display_name, cookie, token, user_agent, 
                    status, created_at, updated_at, notes
                )
                SELECT 
                    platform_id, username, username as display_name, 
                    COALESCE(cookie, '') as cookie,
                    COALESCE(token, '') as token,
                    COALESCE(user_agent, '') as user_agent,
                    CASE 
                        WHEN status = 'active' THEN 'active'
                        WHEN status = 'inactive' THEN 'inactive'
                        ELSE 'inactive'
                    END as status,
                    created_at, updated_at, 
                    CONCAT('从 platform_accounts 迁移，原ID: ', id) as notes
                FROM platform_accounts
                """
                
                result = connection.execute(text(migrate_data_sql))
                migrated_count = result.rowcount
                print(f"✅ 成功迁移 {migrated_count} 条账号数据")
                
        except Exception as e:
            print(f"⚠️ 数据迁移过程中出现问题: {str(e)}")
        
        # 提交事务
        trans.commit()
        print("🎉 爬虫配置表创建完成！")
        
        # 显示表统计信息
        show_table_stats(connection)
        
    except Exception as e:
        trans.rollback()
        print(f"❌ 创建表失败: {str(e)}")
        raise
    finally:
        connection.close()

def show_table_stats(connection):
    """显示表统计信息"""
    print("\n📊 数据库表统计信息:")
    
    tables = ['crawler_accounts', 'crawler_usage_log']
    
    for table in tables:
        try:
            result = connection.execute(text(f"SELECT COUNT(*) FROM {table}"))
            count = result.fetchone()[0]
            print(f"  📋 {table}: {count} 条记录")
        except Exception as e:
            print(f"  ❌ {table}: 查询失败 - {str(e)}")

if __name__ == "__main__":
    create_crawler_tables()
