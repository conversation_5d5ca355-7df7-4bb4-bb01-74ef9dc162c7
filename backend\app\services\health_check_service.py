"""
健康检查服务模块
定期检查系统各组件的健康状态并发送告警
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any
from sqlalchemy.orm import Session
from sqlalchemy import and_, func

from ..database import SessionLocal
from ..models import CrawlerAccount, Platform, ProxyPool, ProxyStatus, ApiCallLog
from .alert_service import alert_service, AlertLevel

logger = logging.getLogger(__name__)

class HealthCheckService:
    """健康检查服务类"""
    
    def __init__(self):
        """初始化健康检查服务"""
        self.check_interval = 300  # 5分钟检查一次
        self.running = False
        
    async def start_health_check(self):
        """启动健康检查"""
        if self.running:
            logger.warning("健康检查已在运行中")
            return
        
        self.running = True
        logger.info("健康检查服务启动")
        
        while self.running:
            try:
                await self.perform_health_check()
                await asyncio.sleep(self.check_interval)
            except Exception as e:
                logger.error(f"健康检查执行失败: {e}")
                await asyncio.sleep(60)  # 出错时等待1分钟再重试
    
    def stop_health_check(self):
        """停止健康检查"""
        self.running = False
        logger.info("健康检查服务停止")
    
    async def perform_health_check(self):
        """执行健康检查"""
        logger.debug("开始执行健康检查")
        
        try:
            db = SessionLocal()
            try:
                # 检查爬虫账号健康状态
                await self._check_crawler_health(db)
                
                # 检查代理健康状态
                await self._check_proxy_health(db)
                
                # 检查API调用健康状态
                await self._check_api_health(db)
                
                # 检查系统资源状态
                await self._check_system_health()

            finally:
                db.close()

        except Exception as e:
            logger.error(f"健康检查失败: {e}")
    
    async def _check_crawler_health(self, db: Session):
        """检查爬虫账号健康状态"""
        try:
            # 获取所有平台
            platforms = db.query(Platform).all()
            
            for platform in platforms:
                # 检查每个平台的可用爬虫数量
                total_accounts = db.query(CrawlerAccount).filter(
                    CrawlerAccount.platform_id == platform.id
                ).count()
                
                active_accounts = db.query(CrawlerAccount).filter(
                    and_(
                        CrawlerAccount.platform_id == platform.id,
                        CrawlerAccount.status == 'active',
                        CrawlerAccount.is_enabled == True,
                        CrawlerAccount.login_status == 'logged_in'
                    )
                ).count()
                
                # 如果可用账号数量过少，发送告警
                if total_accounts > 0:
                    availability_rate = active_accounts / total_accounts
                    if availability_rate < 0.3:  # 可用率低于30%
                        await self._send_crawler_availability_alert(
                            platform, total_accounts, active_accounts, availability_rate
                        )
                elif total_accounts == 0:
                    # 没有配置任何账号
                    await self._send_no_crawler_config_alert(platform)
                    
        except Exception as e:
            logger.error(f"检查爬虫健康状态失败: {e}")
    
    async def _check_proxy_health(self, db: Session):
        """检查代理健康状态"""
        try:
            # 检查可用代理数量
            total_proxies = db.query(ProxyPool).count()
            active_proxies = db.query(ProxyPool).filter(
                ProxyPool.status == ProxyStatus.ACTIVE
            ).count()
            
            if total_proxies > 0:
                availability_rate = active_proxies / total_proxies
                if availability_rate < 0.5:  # 可用率低于50%
                    await self._send_proxy_availability_alert(
                        total_proxies, active_proxies, availability_rate
                    )
            elif total_proxies == 0:
                # 没有配置任何代理
                await self._send_no_proxy_config_alert()
                
        except Exception as e:
            logger.error(f"检查代理健康状态失败: {e}")
    
    async def _check_api_health(self, db: Session):
        """检查API调用健康状态"""
        try:
            # 检查最近1小时的API调用情况
            one_hour_ago = datetime.now() - timedelta(hours=1)
            
            # 总调用次数
            total_calls = db.query(ApiCallLog).filter(
                ApiCallLog.created_at >= one_hour_ago
            ).count()
            
            # 错误调用次数
            error_calls = db.query(ApiCallLog).filter(
                and_(
                    ApiCallLog.created_at >= one_hour_ago,
                    ApiCallLog.response_code >= 400
                )
            ).count()
            
            if total_calls > 10:  # 至少有10次调用才进行检查
                error_rate = error_calls / total_calls
                if error_rate > 0.2:  # 错误率超过20%
                    await self._send_api_error_rate_alert(
                        total_calls, error_calls, error_rate
                    )
            
            # 检查平均响应时间
            avg_response_time = db.query(func.avg(ApiCallLog.response_time_ms)).filter(
                and_(
                    ApiCallLog.created_at >= one_hour_ago,
                    ApiCallLog.response_code < 400
                )
            ).scalar()
            
            if avg_response_time and avg_response_time > 5000:  # 平均响应时间超过5秒
                await self._send_api_performance_degradation_alert(avg_response_time)
                
        except Exception as e:
            logger.error(f"检查API健康状态失败: {e}")
    
    async def _check_system_health(self):
        """检查系统资源状态"""
        try:
            import psutil
            
            # 检查CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            if cpu_percent > 80:
                await self._send_system_resource_alert("CPU", cpu_percent, 80)
            
            # 检查内存使用率
            memory = psutil.virtual_memory()
            if memory.percent > 85:
                await self._send_system_resource_alert("内存", memory.percent, 85)
            
            # 检查磁盘使用率
            disk = psutil.disk_usage('/')
            disk_percent = (disk.used / disk.total) * 100
            if disk_percent > 90:
                await self._send_system_resource_alert("磁盘", disk_percent, 90)
                
        except ImportError:
            logger.debug("psutil未安装，跳过系统资源检查")
        except Exception as e:
            logger.error(f"检查系统资源状态失败: {e}")
    
    async def _send_crawler_availability_alert(self, platform: Platform, total: int, active: int, rate: float):
        """发送爬虫可用性告警"""
        await alert_service.send_crawler_alert(
            title=f"{platform.name}平台爬虫可用性告警",
            message=f"{platform.name}平台爬虫账号可用率过低({rate:.1%})，可能影响数据抓取功能。",
            level=AlertLevel.WARNING,
            statistics=[
                {'name': '总账号数', 'value': str(total), 'unit': '个'},
                {'name': '可用账号数', 'value': str(active), 'unit': '个'},
                {'name': '可用率', 'value': f"{rate:.1%}", 'unit': ''},
                {'name': '告警阈值', 'value': '30%', 'unit': ''}
            ],
            next_actions=[
                "检查失效账号的登录状态",
                "重新登录失效的账号",
                "添加新的爬虫账号",
                "检查账号是否被平台限制"
            ]
        )
    
    async def _send_no_crawler_config_alert(self, platform: Platform):
        """发送无爬虫配置告警"""
        await alert_service.send_crawler_alert(
            title=f"{platform.name}平台无爬虫账号配置",
            message=f"{platform.name}平台尚未配置任何爬虫账号，无法进行数据抓取。",
            level=AlertLevel.ERROR,
            next_actions=[
                f"为{platform.name}平台添加爬虫账号",
                "配置账号登录信息",
                "测试账号可用性"
            ]
        )
    
    async def _send_proxy_availability_alert(self, total: int, active: int, rate: float):
        """发送代理可用性告警"""
        await alert_service.send_proxy_alert(
            title="代理服务器可用性告警",
            message=f"代理服务器可用率过低({rate:.1%})，可能影响爬虫的正常工作。",
            level=AlertLevel.WARNING,
            statistics=[
                {'name': '总代理数', 'value': str(total), 'unit': '个'},
                {'name': '可用代理数', 'value': str(active), 'unit': '个'},
                {'name': '可用率', 'value': f"{rate:.1%}", 'unit': ''},
                {'name': '告警阈值', 'value': '50%', 'unit': ''}
            ]
        )
    
    async def _send_no_proxy_config_alert(self):
        """发送无代理配置告警"""
        await alert_service.send_proxy_alert(
            title="无代理服务器配置",
            message="系统尚未配置任何代理服务器，爬虫可能无法正常工作。",
            level=AlertLevel.WARNING,
            statistics=[
                {'name': '总代理数', 'value': '0', 'unit': '个'},
                {'name': '可用代理数', 'value': '0', 'unit': '个'}
            ]
        )
    
    async def _send_api_error_rate_alert(self, total: int, errors: int, rate: float):
        """发送API错误率告警"""
        await alert_service.send_api_alert(
            title="API接口错误率告警",
            message=f"最近1小时API接口错误率过高({rate:.1%})，需要检查系统状态。",
            level=AlertLevel.WARNING,
            api_info={
                '时间范围': '最近1小时',
                '总调用次数': str(total),
                '错误次数': str(errors),
                '错误率': f"{rate:.1%}",
                '告警阈值': '20%'
            }
        )
    
    async def _send_api_performance_degradation_alert(self, avg_time: float):
        """发送API性能下降告警"""
        await alert_service.send_api_alert(
            title="API接口性能下降告警",
            message=f"最近1小时API接口平均响应时间过长({avg_time:.0f}ms)，可能影响用户体验。",
            level=AlertLevel.WARNING,
            api_info={
                '时间范围': '最近1小时',
                '平均响应时间': f"{avg_time:.0f}ms",
                '性能阈值': '5000ms'
            }
        )
    
    async def _send_system_resource_alert(self, resource_type: str, usage: float, threshold: float):
        """发送系统资源告警"""
        await alert_service.send_system_alert(
            title=f"系统{resource_type}使用率告警",
            message=f"系统{resource_type}使用率过高({usage:.1f}%)，可能影响系统性能。",
            level=AlertLevel.WARNING,
            metrics=[
                {'name': f'{resource_type}使用率', 'value': f"{usage:.1f}", 'unit': '%'},
                {'name': '告警阈值', 'value': f"{threshold:.1f}", 'unit': '%'}
            ],
            recommendations=[
                f"检查占用{resource_type}较高的进程",
                "考虑扩容系统资源",
                "优化系统性能"
            ]
        )

# 全局健康检查服务实例
health_check_service = HealthCheckService()
