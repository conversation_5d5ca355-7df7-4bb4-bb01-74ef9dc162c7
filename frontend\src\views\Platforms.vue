<template>
  <div class="platforms-page">
    <div class="page-header">
      <h2>平台管理</h2>
      <div class="header-actions">
        <button @click="refreshPlatforms" class="btn-secondary">🔄 刷新</button>
        <button @click="showAddModal = true" class="btn-primary">
          ➕ 添加平台
        </button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <div class="stat-card">
        <h3>总平台数</h3>
        <div class="stat-value">{{ stats.total_platforms }}</div>
      </div>
      <div class="stat-card">
        <h3>启用平台</h3>
        <div class="stat-value">{{ stats.enabled_platforms }}</div>
      </div>
      <div class="stat-card">
        <h3>活跃平台</h3>
        <div class="stat-value">{{ stats.active_platforms }}</div>
      </div>
      <div class="stat-card">
        <h3>总成功率</h3>
        <div class="stat-value">{{ stats.success_rate }}%</div>
      </div>
    </div>

    <!-- 筛选器 -->
    <div class="filters">
      <div class="filter-item">
        <label>状态:</label>
        <select v-model="filters.status" @change="loadPlatforms">
          <option value="">全部状态</option>
          <option value="active">活跃</option>
          <option value="inactive">非活跃</option>
          <option value="developing">开发中</option>
          <option value="maintenance">维护中</option>
        </select>
      </div>
      <div class="filter-item">
        <label>
          <input type="checkbox" v-model="filters.enabled_only" @change="loadPlatforms">
          仅显示启用的
        </label>
      </div>
      <div class="filter-item">
        <button @click="refreshPlatforms" class="btn-secondary">🔄 刷新</button>
      </div>
    </div>

    <!-- 平台列表 -->
    <div class="platforms-table">
      <div class="table-header">
        <div class="col">ID</div>
        <div class="col">代码</div>
        <div class="col">名称</div>
        <div class="col">状态</div>
        <div class="col">启用状态</div>
        <div class="col">优先级</div>
        <div class="col">总请求</div>
        <div class="col">成功请求</div>
        <div class="col">成功率</div>
        <div class="col">限制/分钟</div>
        <div class="col">超时(秒)</div>
        <div class="col">创建时间</div>
        <div class="col">操作</div>
      </div>
      
      <div v-if="loading" class="loading">加载中...</div>
      
      <div v-else-if="platforms.length === 0" class="empty">
        暂无平台数据
      </div>
      
      <div v-else>
        <div v-for="platform in platforms" :key="platform.id" class="table-row">
          <div class="col">{{ platform.id }}</div>
          <div class="col">
            <span class="code-tag">{{ platform.code }}</span>
          </div>
          <div class="col">
            <div class="platform-info">
              <div class="platform-name">{{ platform.name }}</div>
              <div class="platform-name-en" v-if="platform.name_en">{{ platform.name_en }}</div>
            </div>
          </div>
          <div class="col">
            <span class="status-tag" :class="platform.status">
              {{ getStatusName(platform.status) }}
            </span>
          </div>
          <div class="col">
            <button @click="togglePlatform(platform)" class="toggle-btn" :class="{ enabled: platform.is_enabled }">
              {{ platform.is_enabled ? '✓ 启用' : '✗ 禁用' }}
            </button>
          </div>
          <div class="col">{{ platform.priority }}</div>
          <div class="col">{{ platform.total_requests }}</div>
          <div class="col">{{ platform.successful_requests }}</div>
          <div class="col">{{ calculateSuccessRate(platform) }}%</div>
          <div class="col">{{ platform.rate_limit }}</div>
          <div class="col">{{ platform.timeout }}</div>
          <div class="col">{{ formatDate(platform.created_at) }}</div>
          <div class="col actions">
            <button @click="viewPlatform(platform)" class="btn-view">👁️ 查看</button>
            <button @click="editPlatform(platform)" class="btn-edit">✏️ 编辑</button>
            <button @click="deletePlatform(platform)" class="btn-delete">🗑️ 删除</button>
          </div>
        </div>
      </div>
    </div>

    <!-- 分页 -->
    <div class="pagination" v-if="totalPages > 1">
      <button @click="changePage(currentPage - 1)" :disabled="currentPage === 1">上一页</button>
      <span>第 {{ currentPage }} 页，共 {{ totalPages }} 页</span>
      <button @click="changePage(currentPage + 1)" :disabled="currentPage === totalPages">下一页</button>
    </div>

    <!-- 添加/编辑平台模态框 -->
    <div v-if="showAddModal || showEditModal" class="modal-overlay" @click="closeModal">
      <div class="modal" @click.stop>
        <div class="modal-header">
          <h3>{{ showAddModal ? '添加平台' : '编辑平台' }}</h3>
          <button @click="closeModal" class="close-btn">✕</button>
        </div>
        <div class="modal-body">
          <form @submit.prevent="savePlatform">
            <div class="form-row">
              <div class="form-group">
                <label>平台代码 *</label>
                <input v-model="platformForm.code" type="text" required placeholder="如: taobao">
              </div>
              <div class="form-group">
                <label>平台名称 *</label>
                <input v-model="platformForm.name" type="text" required placeholder="如: 淘宝">
              </div>
            </div>
            <div class="form-row">
              <div class="form-group">
                <label>英文名称</label>
                <input v-model="platformForm.name_en" type="text" placeholder="如: Taobao">
              </div>
              <div class="form-group">
                <label>官网地址</label>
                <input v-model="platformForm.website_url" type="url" placeholder="https://www.taobao.com">
              </div>
            </div>
            <div class="form-group">
              <label>Logo地址</label>
              <input v-model="platformForm.logo_url" type="url" placeholder="Logo图片URL">
            </div>
            <div class="form-group">
              <label>平台描述</label>
              <textarea v-model="platformForm.description" rows="3" placeholder="平台描述信息"></textarea>
            </div>
            <div class="form-row">
              <div class="form-group">
                <label>状态</label>
                <select v-model="platformForm.status">
                  <option value="developing">开发中</option>
                  <option value="active">活跃</option>
                  <option value="inactive">非活跃</option>
                  <option value="maintenance">维护中</option>
                </select>
              </div>
              <div class="form-group">
                <label>优先级</label>
                <input v-model="platformForm.priority" type="number" min="1" max="100">
              </div>
            </div>
            <div class="form-row">
              <div class="form-group">
                <label>限制/分钟</label>
                <input v-model="platformForm.rate_limit" type="number" min="1">
              </div>
              <div class="form-group">
                <label>超时时间(秒)</label>
                <input v-model="platformForm.timeout" type="number" min="1">
              </div>
            </div>
            <div class="form-group">
              <label>
                <input type="checkbox" v-model="platformForm.is_enabled">
                启用平台
              </label>
            </div>
            <div class="form-actions">
              <button type="button" @click="closeModal" class="btn-secondary">取消</button>
              <button type="submit" class="btn-primary" :disabled="saving">
                {{ saving ? '保存中...' : '保存' }}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <!-- 查看平台详情模态框 -->
    <div v-if="showViewModal" class="modal-overlay" @click="closeViewModal">
      <div class="modal detail-modal" @click.stop>
        <div class="modal-header">
          <h3>平台详情 - {{ selectedPlatform?.name }}</h3>
          <button @click="closeViewModal" class="close-btn">✕</button>
        </div>
        <div class="modal-body">
          <div class="detail-grid" v-if="selectedPlatform">
            <div class="detail-item">
              <label>平台ID:</label>
              <span>{{ selectedPlatform.id }}</span>
            </div>
            <div class="detail-item">
              <label>平台代码:</label>
              <span class="code-tag">{{ selectedPlatform.code }}</span>
            </div>
            <div class="detail-item">
              <label>平台名称:</label>
              <span>{{ selectedPlatform.name }}</span>
            </div>
            <div class="detail-item">
              <label>英文名称:</label>
              <span>{{ selectedPlatform.name_en || '-' }}</span>
            </div>
            <div class="detail-item">
              <label>官网地址:</label>
              <a v-if="selectedPlatform.website_url" :href="selectedPlatform.website_url" target="_blank">
                {{ selectedPlatform.website_url }}
              </a>
              <span v-else>-</span>
            </div>
            <div class="detail-item">
              <label>状态:</label>
              <span class="status-tag" :class="selectedPlatform.status">
                {{ getStatusName(selectedPlatform.status) }}
              </span>
            </div>
            <div class="detail-item">
              <label>启用状态:</label>
              <span :class="selectedPlatform.is_enabled ? 'text-success' : 'text-danger'">
                {{ selectedPlatform.is_enabled ? '已启用' : '已禁用' }}
              </span>
            </div>
            <div class="detail-item">
              <label>优先级:</label>
              <span>{{ selectedPlatform.priority }}</span>
            </div>
            <div class="detail-item">
              <label>限制/分钟:</label>
              <span>{{ selectedPlatform.rate_limit }}</span>
            </div>
            <div class="detail-item">
              <label>超时时间:</label>
              <span>{{ selectedPlatform.timeout }}秒</span>
            </div>
            <div class="detail-item">
              <label>总请求数:</label>
              <span>{{ selectedPlatform.total_requests }}</span>
            </div>
            <div class="detail-item">
              <label>成功请求:</label>
              <span>{{ selectedPlatform.successful_requests }}</span>
            </div>
            <div class="detail-item">
              <label>失败请求:</label>
              <span>{{ selectedPlatform.failed_requests }}</span>
            </div>
            <div class="detail-item">
              <label>成功率:</label>
              <span>{{ calculateSuccessRate(selectedPlatform) }}%</span>
            </div>
            <div class="detail-item">
              <label>创建时间:</label>
              <span>{{ formatDate(selectedPlatform.created_at) }}</span>
            </div>
            <div class="detail-item">
              <label>更新时间:</label>
              <span>{{ formatDate(selectedPlatform.updated_at) }}</span>
            </div>
            <div class="detail-item full-width">
              <label>平台描述:</label>
              <div class="description">{{ selectedPlatform.description || '-' }}</div>
            </div>
            <div class="detail-item full-width" v-if="selectedPlatform.crawler_config">
              <label>爬虫配置:</label>
              <pre class="config-json">{{ JSON.stringify(selectedPlatform.crawler_config, null, 2) }}</pre>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import api from '../api'

// 响应式数据
const platforms = ref([])
const stats = ref({
  total_platforms: 0,
  enabled_platforms: 0,
  active_platforms: 0,
  success_rate: 0
})
const loading = ref(false)
const saving = ref(false)
const showAddModal = ref(false)
const showEditModal = ref(false)
const showViewModal = ref(false)
const selectedPlatform = ref(null)
const currentPage = ref(1)
const totalPages = ref(1)
const pageSize = 20

// 筛选器
const filters = ref({
  status: '',
  enabled_only: false
})

// 平台表单
const platformForm = ref({
  id: null,
  code: '',
  name: '',
  name_en: '',
  website_url: '',
  logo_url: '',
  description: '',
  status: 'developing',
  is_enabled: true,
  priority: 1,
  rate_limit: 60,
  timeout: 30,
  crawler_config: {}
})

// 状态名称映射
const getStatusName = (status) => {
  const names = {
    'active': '活跃',
    'inactive': '非活跃',
    'developing': '开发中',
    'maintenance': '维护中'
  }
  return names[status] || status
}

// 计算成功率
const calculateSuccessRate = (platform) => {
  if (platform.total_requests === 0) return 0
  return Math.round((platform.successful_requests / platform.total_requests) * 100)
}

// 格式化日期
const formatDate = (dateStr) => {
  if (!dateStr) return '-'
  return new Date(dateStr).toLocaleString('zh-CN')
}

// 加载平台列表
const loadPlatforms = async () => {
  loading.value = true
  try {
    const response = await api.platforms.getAll()
    if (response.code === 200) {
      platforms.value = response.data
      totalPages.value = Math.ceil(platforms.value.length / pageSize)
    } else {
      platforms.value = []
    }
  } catch (error) {
    console.error('加载平台失败:', error)
    showMessage('错误', '加载平台失败')
    platforms.value = []
  } finally {
    loading.value = false
  }
}

// 加载统计信息
const loadStats = async () => {
  try {
    const response = await api.stats.getOverview()
    if (response.code === 200) {
      stats.value = response.data
    }
  } catch (error) {
    console.error('加载统计信息失败:', error)
  }
}

// 刷新平台列表
const refreshPlatforms = () => {
  currentPage.value = 1
  loadPlatforms()
  loadStats()
}

// 分页
const changePage = (page) => {
  if (page >= 1 && page <= totalPages.value) {
    currentPage.value = page
    loadPlatforms()
  }
}

// 查看平台详情
const viewPlatform = (platform) => {
  selectedPlatform.value = platform
  showViewModal.value = true
}

// 编辑平台
const editPlatform = (platform) => {
  platformForm.value = {
    id: platform.id,
    code: platform.code,
    name: platform.name,
    name_en: platform.name_en || '',
    website_url: platform.website_url || '',
    logo_url: platform.logo_url || '',
    description: platform.description || '',
    status: platform.status,
    is_enabled: platform.is_enabled,
    priority: platform.priority,
    rate_limit: platform.rate_limit,
    timeout: platform.timeout,
    crawler_config: platform.crawler_config || {}
  }
  showEditModal.value = true
}

// 切换平台启用状态
const togglePlatform = async (platform) => {
  try {
    const updateData = { ...platform, is_enabled: !platform.is_enabled }
    const response = await api.platforms.update(platform.id, updateData)
    if (response.code === 200) {
      showMessage('成功', '平台状态切换成功')
      loadPlatforms()
      loadStats()
    }
  } catch (error) {
    console.error('切换平台状态失败:', error)
    showMessage('错误', '切换平台状态失败')
  }
}

// 删除平台
const deletePlatform = async (platform) => {
  if (!confirm(`确定要删除平台 "${platform.name}" 吗？`)) {
    return
  }

  try {
    const response = await api.platforms.delete(platform.id)
    if (response.code === 200) {
      showMessage('成功', '平台删除成功')
      loadPlatforms()
      loadStats()
    }
  } catch (error) {
    console.error('删除平台失败:', error)
    showMessage('错误', '删除平台失败')
  }
}

// 保存平台
const savePlatform = async () => {
  saving.value = true
  try {
    let response
    if (showAddModal.value) {
      // 添加平台
      response = await api.platforms.create(platformForm.value)
      if (response.code === 200) {
        showMessage('成功', '平台添加成功')
      }
    } else {
      // 编辑平台
      const updateData = { ...platformForm.value }
      delete updateData.id
      response = await api.platforms.update(platformForm.value.id, updateData)
      if (response.code === 200) {
        showMessage('成功', '平台更新成功')
      }
    }
    closeModal()
    loadPlatforms()
    loadStats()
  } catch (error) {
    console.error('保存平台失败:', error)
    showMessage('错误', '保存平台失败')
  } finally {
    saving.value = false
  }
}

// 关闭模态框
const closeModal = () => {
  showAddModal.value = false
  showEditModal.value = false
  platformForm.value = {
    id: null,
    code: '',
    name: '',
    name_en: '',
    website_url: '',
    logo_url: '',
    description: '',
    status: 'developing',
    is_enabled: true,
    priority: 1,
    rate_limit: 60,
    timeout: 30,
    crawler_config: {}
  }
}

// 关闭查看模态框
const closeViewModal = () => {
  showViewModal.value = false
  selectedPlatform.value = null
}

// 简单的消息提示
const showMessage = (type, text) => {
  alert(`${type}: ${text}`)
}

// 页面加载时获取数据
onMounted(() => {
  loadPlatforms()
  loadStats()
})
</script>

<style scoped>
.platforms-page {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #333;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.stats-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 20px;
}

.stat-card {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  text-align: center;
}

.stat-card h3 {
  margin: 0 0 8px 0;
  color: #666;
  font-size: 14px;
  font-weight: normal;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #1890ff;
}

.filters {
  display: flex;
  gap: 16px;
  margin-bottom: 20px;
  padding: 16px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  flex-wrap: wrap;
}

.filter-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-item label {
  font-size: 14px;
  color: #666;
  white-space: nowrap;
}

.filter-item select {
  padding: 6px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.platforms-table {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  overflow: hidden;
  overflow-x: auto;
}

.table-header {
  display: grid;
  grid-template-columns: 50px 70px 120px 70px 80px 50px 70px 70px 50px 70px 70px 120px 160px;
  background: #f5f5f5;
  border-bottom: 1px solid #eee;
  font-weight: bold;
  color: #333;
  min-width: 1000px;
}

.table-row {
  display: grid;
  grid-template-columns: 50px 70px 120px 70px 80px 50px 70px 70px 50px 70px 70px 120px 160px;
  border-bottom: 1px solid #eee;
  min-width: 1000px;
}

.table-row:hover {
  background: #f9f9f9;
}

.col {
  padding: 12px 8px;
  font-size: 14px;
  display: flex;
  align-items: center;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.code-tag {
  padding: 2px 8px;
  background: #f0f0f0;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-family: monospace;
  font-size: 12px;
  font-weight: bold;
}

.platform-info {
  display: flex;
  flex-direction: column;
}

.platform-name {
  font-weight: bold;
  color: #333;
}

.platform-name-en {
  font-size: 12px;
  color: #666;
}

.status-tag {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: bold;
}

.status-tag.active {
  background: #f6ffed;
  color: #52c41a;
  border: 1px solid #b7eb8f;
}

.status-tag.inactive {
  background: #f5f5f5;
  color: #999;
  border: 1px solid #d9d9d9;
}

.status-tag.developing {
  background: #e6f7ff;
  color: #1890ff;
  border: 1px solid #91d5ff;
}

.status-tag.maintenance {
  background: #fff7e6;
  color: #fa8c16;
  border: 1px solid #ffd591;
}

.toggle-btn {
  padding: 4px 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: #f5f5f5;
  cursor: pointer;
  font-size: 12px;
  color: #666;
}

.toggle-btn.enabled {
  background: #f6ffed;
  color: #52c41a;
  border-color: #b7eb8f;
}

.toggle-btn:hover {
  opacity: 0.8;
}

.actions {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

.actions button {
  padding: 4px 8px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  white-space: nowrap;
}

.btn-view {
  background: #722ed1;
  color: white;
}

.btn-view:hover {
  background: #9254de;
}

.btn-edit {
  background: #52c41a;
  color: white;
}

.btn-edit:hover {
  background: #73d13d;
}

.btn-delete {
  background: #ff4d4f;
  color: white;
}

.btn-delete:hover {
  background: #ff7875;
}

.btn-primary {
  padding: 8px 16px;
  background: #1890ff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.btn-primary:hover {
  background: #40a9ff;
}

.btn-primary:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.btn-secondary {
  padding: 8px 16px;
  background: #f5f5f5;
  color: #333;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.btn-secondary:hover {
  background: #e6f7ff;
  border-color: #1890ff;
}

.loading, .empty {
  text-align: center;
  padding: 40px;
  color: #666;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16px;
  margin-top: 20px;
  padding: 16px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.pagination button {
  padding: 8px 16px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: white;
  cursor: pointer;
}

.pagination button:hover {
  background: #f5f5f5;
}

.pagination button:disabled {
  background: #f5f5f5;
  color: #ccc;
  cursor: not-allowed;
}

/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal {
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  width: 600px;
  max-width: 90vw;
  max-height: 90vh;
  overflow: auto;
}

.detail-modal {
  width: 900px;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #eee;
}

.modal-header h3 {
  margin: 0;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  color: #999;
  padding: 4px;
}

.close-btn:hover {
  color: #333;
}

.modal-body {
  padding: 20px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.form-group {
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  margin-bottom: 4px;
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.form-group textarea {
  resize: vertical;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #eee;
}

.detail-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.detail-item.full-width {
  grid-column: 1 / -1;
}

.detail-item label {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.detail-item span {
  font-size: 14px;
  color: #333;
  word-break: break-all;
}

.detail-item a {
  color: #1890ff;
  text-decoration: none;
}

.detail-item a:hover {
  text-decoration: underline;
}

.text-success {
  color: #52c41a;
  font-weight: bold;
}

.text-danger {
  color: #ff4d4f;
  font-weight: bold;
}

.description {
  background: #f9f9f9;
  border: 1px solid #eee;
  border-radius: 4px;
  padding: 12px;
  color: #333;
  white-space: pre-wrap;
}

.config-json {
  background: #f6f8fa;
  border: 1px solid #e1e4e8;
  border-radius: 4px;
  padding: 12px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  color: #333;
  overflow-x: auto;
  white-space: pre;
}
</style>
