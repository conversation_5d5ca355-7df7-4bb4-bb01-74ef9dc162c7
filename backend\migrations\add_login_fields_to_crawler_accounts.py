"""
添加登录相关字段到爬虫账号表

为支持自动登录和token管理功能
"""

import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import text
from app.database import engine


def add_login_fields():
    """添加登录相关字段"""
    try:

        with engine.connect() as connection:
            # 检查字段是否已存在
            result = connection.execute(text("""
                SELECT COLUMN_NAME
                FROM INFORMATION_SCHEMA.COLUMNS
                WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'crawler_accounts'
            """))

            existing_columns = [row[0] for row in result.fetchall()]
            print(f"现有字段: {existing_columns}")

            # 需要添加的字段
            fields_to_add = [
                ("password", "VARCHAR(255) NULL COMMENT '加密存储的密码'"),
                ("proxy_id", "BIGINT NULL COMMENT '关联代理ID'"),
                ("login_status", "ENUM('not_logged_in', 'logged_in', 'login_failed', 'expired') DEFAULT 'not_logged_in' COMMENT '登录状态'"),
                ("token_expires_at", "DATETIME NULL COMMENT 'token过期时间'"),
                ("auto_refresh_enabled", "BOOLEAN DEFAULT TRUE COMMENT '是否启用自动刷新'"),
                ("last_login_at", "DATETIME NULL COMMENT '最后登录时间'")
            ]

            # 添加不存在的字段
            for field_name, field_definition in fields_to_add:
                if field_name not in existing_columns:
                    alter_sql = f"ALTER TABLE crawler_accounts ADD COLUMN {field_name} {field_definition}"
                    print(f"添加字段: {field_name}")
                    connection.execute(text(alter_sql))
                    connection.commit()
                else:
                    print(f"字段 {field_name} 已存在，跳过")

            # 修改cookie字段为可空
            if 'cookie' in existing_columns:
                try:
                    connection.execute(text("ALTER TABLE crawler_accounts MODIFY COLUMN cookie TEXT NULL COMMENT 'Cookie信息'"))
                    connection.commit()
                    print("修改cookie字段为可空")
                except Exception as e:
                    print(f"修改cookie字段时出错（可忽略）: {e}")

            # 添加索引
            indexes_to_add = [
                ("idx_login_status", "login_status"),
                ("idx_token_expires", "token_expires_at"),
                ("idx_proxy_id", "proxy_id")
            ]

            for index_name, column_name in indexes_to_add:
                try:
                    connection.execute(text(f"CREATE INDEX {index_name} ON crawler_accounts ({column_name})"))
                    connection.commit()
                    print(f"添加索引: {index_name}")
                except Exception as e:
                    if "Duplicate key name" not in str(e):
                        print(f"添加索引 {index_name} 时出错（可忽略）: {e}")

            print("✅ 数据库表结构更新完成")
            return True

    except Exception as e:
        print(f"❌ 更新数据库表结构失败: {e}")
        return False


def main():
    """主函数"""
    print("🔧 开始更新爬虫账号表结构...")
    print(f"⏰ 执行时间: {datetime.now()}")
    
    if add_login_fields():
        print("🎉 数据库迁移成功完成！")
    else:
        print("💥 数据库迁移失败！")


if __name__ == "__main__":
    main()
