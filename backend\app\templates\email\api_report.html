{% extends "base.html" %}

{% block title %}API调用报告 - AqentCrawler{% endblock %}

{% block header_title %}API调用报告{% endblock %}
{% block header_subtitle %}系统API使用情况统计报告{% endblock %}

{% block content %}
<div class="alert alert-info">
    <h3 style="margin: 0 0 10px 0;">📊 {{ report_title or 'API调用统计报告' }}</h3>
    <p style="margin: 0;">{{ report_description or '以下是系统API调用的详细统计信息' }}</p>
</div>

{% if report_period %}
<p style="background: #f8f9fa; padding: 15px; border-radius: 4px; margin: 20px 0;">
    <strong>报告周期:</strong> {{ report_period.start }} 至 {{ report_period.end }}<br>
    <strong>统计时长:</strong> {{ report_period.duration or '24小时' }}
</p>
{% endif %}

{% if summary_stats %}
<h4>总体统计</h4>
<table class="data-table">
    {% for stat in summary_stats %}
    <tr>
        <td style="font-weight: 600; width: 40%;">{{ stat.name }}</td>
        <td>
            {{ stat.value }}
            {% if stat.unit %}{{ stat.unit }}{% endif %}
            {% if stat.trend %}
            <small style="color: {% if stat.trend == 'up' %}#52c41a{% elif stat.trend == 'down' %}#ff4d4f{% else %}#666{% endif %};">
                {% if stat.trend == 'up' %}↗️{% elif stat.trend == 'down' %}↘️{% else %}➡️{% endif %}
                {% if stat.change %}{{ stat.change }}{% endif %}
            </small>
            {% endif %}
        </td>
    </tr>
    {% endfor %}
</table>
{% endif %}

{% if api_endpoints %}
<h4>API端点统计</h4>
<table class="data-table">
    <thead>
        <tr>
            <th>API端点</th>
            <th>调用次数</th>
            <th>成功率</th>
            <th>平均响应时间</th>
            <th>状态</th>
        </tr>
    </thead>
    <tbody>
        {% for endpoint in api_endpoints %}
        <tr>
            <td>{{ endpoint.path }}</td>
            <td>{{ endpoint.total_calls }}</td>
            <td>
                <span style="color: {% if endpoint.success_rate >= 95 %}#52c41a{% elif endpoint.success_rate >= 90 %}#faad14{% else %}#ff4d4f{% endif %};">
                    {{ endpoint.success_rate }}%
                </span>
            </td>
            <td>{{ endpoint.avg_response_time }}ms</td>
            <td>
                <span class="status-badge status-{% if endpoint.success_rate >= 95 %}success{% elif endpoint.success_rate >= 90 %}warning{% else %}error{% endif %}">
                    {% if endpoint.success_rate >= 95 %}正常{% elif endpoint.success_rate >= 90 %}警告{% else %}异常{% endif %}
                </span>
            </td>
        </tr>
        {% endfor %}
    </tbody>
</table>
{% endif %}

{% if platform_stats %}
<h4>平台调用统计</h4>
<table class="data-table">
    <thead>
        <tr>
            <th>平台</th>
            <th>搜索调用</th>
            <th>详情调用</th>
            <th>成功率</th>
            <th>错误次数</th>
        </tr>
    </thead>
    <tbody>
        {% for platform in platform_stats %}
        <tr>
            <td>{{ platform.name }}</td>
            <td>{{ platform.search_calls or 0 }}</td>
            <td>{{ platform.detail_calls or 0 }}</td>
            <td>
                <span style="color: {% if platform.success_rate >= 95 %}#52c41a{% elif platform.success_rate >= 90 %}#faad14{% else %}#ff4d4f{% endif %};">
                    {{ platform.success_rate }}%
                </span>
            </td>
            <td>
                <span style="color: {% if platform.error_count > 10 %}#ff4d4f{% elif platform.error_count > 5 %}#faad14{% else %}#52c41a{% endif %};">
                    {{ platform.error_count or 0 }}
                </span>
            </td>
        </tr>
        {% endfor %}
    </tbody>
</table>
{% endif %}

{% if error_analysis %}
<h4>错误分析</h4>
{% if error_analysis.top_errors %}
<table class="data-table">
    <thead>
        <tr>
            <th>错误类型</th>
            <th>出现次数</th>
            <th>占比</th>
            <th>最近发生</th>
        </tr>
    </thead>
    <tbody>
        {% for error in error_analysis.top_errors %}
        <tr>
            <td>{{ error.type or error.message }}</td>
            <td>{{ error.count }}</td>
            <td>{{ error.percentage }}%</td>
            <td class="timestamp">{{ error.last_occurrence }}</td>
        </tr>
        {% endfor %}
    </tbody>
</table>
{% endif %}
{% endif %}

{% if performance_metrics %}
<h4>性能指标</h4>
<table class="data-table">
    {% for metric in performance_metrics %}
    <tr>
        <td style="font-weight: 600; width: 40%;">{{ metric.name }}</td>
        <td>
            {{ metric.value }}
            {% if metric.unit %}{{ metric.unit }}{% endif %}
            {% if metric.benchmark %}
            <small style="color: {% if metric.is_good %}#52c41a{% else %}#ff4d4f{% endif %};">
                (基准: {{ metric.benchmark }}{{ metric.unit or '' }})
            </small>
            {% endif %}
        </td>
    </tr>
    {% endfor %}
</table>
{% endif %}

{% if recommendations %}
<h4>优化建议</h4>
<ul>
    {% for recommendation in recommendations %}
    <li>
        <strong>{{ recommendation.title }}:</strong> {{ recommendation.description }}
        {% if recommendation.priority %}
        <span class="status-badge status-{% if recommendation.priority == 'high' %}error{% elif recommendation.priority == 'medium' %}warning{% else %}info{% endif %}">
            {% if recommendation.priority == 'high' %}高优先级{% elif recommendation.priority == 'medium' %}中优先级{% else %}低优先级{% endif %}
        </span>
        {% endif %}
    </li>
    {% endfor %}
</ul>
{% endif %}

{% if dashboard_url %}
<div style="text-align: center; margin: 30px 0;">
    <a href="{{ dashboard_url }}" class="btn btn-primary">查看详细报告</a>
</div>
{% endif %}

<div class="divider"></div>

<p style="font-size: 14px; color: #666;">
    <strong>报告生成时间:</strong> {{ report_time or now().strftime('%Y-%m-%d %H:%M:%S') }}<br>
    <strong>报告类型:</strong> {{ report_type or '日常统计' }}<br>
    {% if next_report %}
    <strong>下次报告:</strong> {{ next_report }}
    {% endif %}
</p>
{% endblock %}
