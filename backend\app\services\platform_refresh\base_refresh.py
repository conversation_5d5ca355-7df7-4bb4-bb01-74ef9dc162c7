"""
基础平台Token刷新类

定义所有平台Token刷新服务的通用接口和基础功能
"""

import re
from abc import ABC, abstractmethod
from datetime import datetime, timedelta
from typing import Dict, Optional, Tuple

from playwright.sync_api import Page

from app.models import CrawlerAccount


class BasePlatformRefresh(ABC):
    """平台Token刷新基础类"""

    def __init__(self, platform_code: str):
        self.platform_code = platform_code
        self.refresh_timeout = 300  # 5分钟刷新超时
        self.check_interval = 2     # 2秒检查一次Token状态

    @abstractmethod
    def get_homepage_url(self) -> str:
        """获取平台主页URL"""
        pass

    @abstractmethod
    def extract_token_from_cookies(self, cookies: list) -> Tuple[Optional[str], Optional[datetime]]:
        """从Cookie中提取Token和过期时间"""
        pass

    def setup_browser_context(self, context):
        """设置浏览器上下文（反检测等）"""
        # 应用反检测脚本
        context.add_init_script("""
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined,
            });
            
            window.chrome = {
                runtime: {},
                loadTimes: function() {},
                csi: function() {},
                app: {}
            };
            
            Object.defineProperty(navigator, 'plugins', {
                get: () => [1, 2, 3, 4, 5],
            });
            
            Object.defineProperty(navigator, 'languages', {
                get: () => ['zh-CN', 'zh', 'en'],
            });
        """)

    def wait_for_token_refresh(self, page: Page, old_token: str) -> Dict[str, any]:
        """等待Token刷新完成"""
        token_found = False
        elapsed_time = 0
        current_token = None
        current_token_expires_at = None

        print("开始检测Token状态...")
        if old_token:
            print(f"旧Token: {old_token[:10]}...")
            print(f"系统将每{self.check_interval}秒检测一次Token状态，最多等待{self.refresh_timeout}秒...")
        else:
            print("未找到旧Token，将获取当前Token")
            print(f"系统将每{self.check_interval}秒检测一次Token状态，最多等待{self.refresh_timeout}秒...")

        while elapsed_time < self.refresh_timeout and not token_found:
            try:
                print(f"检查Token状态 (已等待{elapsed_time}秒)")

                # 检查页面和浏览器是否还存在
                try:
                    if page.is_closed():
                        print("检测到页面已关闭，用户可能关闭了浏览器")
                        return {"success": False, "message": "用户关闭了浏览器", "cancelled": True}
                except:
                    print("检测到浏览器已关闭，用户可能关闭了浏览器")
                    return {"success": False, "message": "用户关闭了浏览器", "cancelled": True}

                # 获取当前Cookie
                cookies = page.context.cookies()
                print(f"获取到 {len(cookies)} 个Cookie")

                # 打印关键Cookie信息（用于调试）
                if hasattr(self, 'get_critical_cookies'):
                    critical_cookies = self.get_critical_cookies()
                    cookie_dict_debug = {cookie['name']: cookie['value'] for cookie in cookies}
                    for cookie_name in critical_cookies[:5]:  # 只显示前5个关键Cookie
                        if cookie_name in cookie_dict_debug:
                            print(f"关键Cookie {cookie_name}: {cookie_dict_debug[cookie_name][:20]}...")
                        else:
                            print(f"关键Cookie {cookie_name}: 缺失")
                else:
                    # 默认检查的关键Cookie
                    key_cookies = ['_m_h5_tk', 'cookie2', 'wk_cookie2']
                    for cookie in cookies:
                        if cookie['name'] in key_cookies:
                            print(f"关键Cookie {cookie['name']}: {cookie['value'][:20]}...")

                current_token, current_token_expires_at = self.extract_token_from_cookies(cookies)

                if current_token:
                    print(f"当前Token: {current_token[:10]}...")
                    print(f"旧Token: {old_token[:10] if old_token else 'None'}...")

                    # 如果没有旧Token，或者Token已更新，则认为成功
                    if not old_token or current_token != old_token:
                        if old_token and current_token != old_token:
                            print(f"检测到新Token: {current_token[:10]}... (与旧Token不同)")
                        elif not old_token:
                            print(f"获取到Token: {current_token[:10]}... (首次获取)")

                        if current_token_expires_at:
                            print(f"Token过期时间: {current_token_expires_at}")
                        token_found = True
                        break
                    else:
                        print(f"Token未更新 (当前: {current_token[:10]}... = 旧: {old_token[:10]}...)，继续等待...")
                else:
                    print("未检测到Token，继续等待...")

                page.wait_for_timeout(self.check_interval * 1000)
                elapsed_time += self.check_interval

            except Exception as e:
                error_msg = str(e)
                print(f"检查Token状态时出错: {error_msg}")

                # 检查是否是浏览器关闭相关的错误
                if any(keyword in error_msg.lower() for keyword in ['closed', 'disconnected', 'target', 'browser']):
                    print("检测到浏览器关闭相关错误，用户可能关闭了浏览器")
                    return {"success": False, "message": "用户关闭了浏览器", "cancelled": True}

                page.wait_for_timeout(self.check_interval * 1000)
                elapsed_time += self.check_interval

        if not token_found:
            return {"success": False, "message": f"Token获取超时（{self.refresh_timeout}秒），未检测到有效Token"}

        return {
            "success": True,
            "message": "Token获取成功",
            "new_token": current_token,
            "new_token_expires_at": current_token_expires_at
        }

    def extract_cookies_and_token(self, page: Page) -> Dict[str, any]:
        """提取Cookie和Token"""
        try:
            # 获取所有Cookie
            cookies = page.context.cookies()
            cookie_dict = {cookie['name']: cookie['value'] for cookie in cookies}

            # 提取Token
            token, token_expires_at = self.extract_token_from_cookies(cookies)

            # 格式化cookie字符串
            cookie_string = "; ".join([f"{name}={value}" for name, value in cookie_dict.items()])

            # 应用平台特定的Cookie过滤（如果子类实现了filter_cookies方法）
            if hasattr(self, 'filter_cookies'):
                filtered_cookie_string = self.filter_cookies(cookie_string)
                # 计算过滤后的Cookie数量（排除空字符串）
                filtered_count = len([c for c in filtered_cookie_string.split(';') if c.strip()])
                print(f"Cookie过滤: {len(cookie_dict)} -> {filtered_count} 个关键Cookie")
                cookie_string = filtered_cookie_string

            # 获取User-Agent
            user_agent = page.evaluate("navigator.userAgent")

            print(f"提取到 {len(cookie_dict)} 个Cookie")
            print(f"Token: {token[:10] if token else 'None'}...")

            # 详细的Token信息
            if token:
                print(f"Token完整长度: {len(token)} 字符")
                if token_expires_at:
                    from datetime import datetime
                    now = datetime.now()
                    time_diff = (token_expires_at - now).total_seconds()
                    print(f"Token过期时间: {token_expires_at}")
                    print(f"Token剩余时间: {time_diff:.0f} 秒")
                else:
                    print("Token过期时间: 未设置")
            else:
                print("警告: 未能提取到Token")

            # 打印关键Cookie信息用于调试
            if hasattr(self, 'get_critical_cookies'):
                critical_cookies = self.get_critical_cookies()
                print("关键Cookie状态:")
                missing_cookies = []
                present_cookies = []
                for cookie_name in critical_cookies:
                    if cookie_name in cookie_dict:
                        value = cookie_dict[cookie_name]
                        print(f"  [OK] {cookie_name}: {value[:20]}...")
                        present_cookies.append(cookie_name)
                    else:
                        print(f"  [MISS] {cookie_name}: 缺失")
                        missing_cookies.append(cookie_name)

                print(f"Cookie完整性: {len(present_cookies)}/{len(critical_cookies)} 个关键Cookie存在")
                if missing_cookies:
                    print(f"缺失的关键Cookie: {missing_cookies}")

                # 检查过滤后的Cookie字符串
                if hasattr(self, 'filter_cookies'):
                    print(f"过滤后Cookie字符串长度: {len(cookie_string)} 字符")
                    print(f"过滤后Cookie内容预览: {cookie_string[:100]}...")

                    # 验证过滤后的Cookie是否包含所有关键Cookie
                    filtered_cookie_names = []
                    for cookie_pair in cookie_string.split(';'):
                        if '=' in cookie_pair.strip():
                            name = cookie_pair.strip().split('=')[0]
                            filtered_cookie_names.append(name)

                    filtered_missing = [c for c in critical_cookies if c not in filtered_cookie_names]
                    if filtered_missing:
                        print(f"过滤后仍缺失的关键Cookie: {filtered_missing}")
                    else:
                        print("过滤后所有关键Cookie都存在")

            return {
                "success": True,
                "cookie": cookie_string,
                "token": token,
                "token_expires_at": token_expires_at,
                "user_agent": user_agent,
                "cookie_count": len(cookie_dict)
            }

        except Exception as e:
            print(f"提取Cookie和Token失败: {str(e)}")
            return {"success": False, "message": f"提取Cookie和Token失败: {str(e)}"}

    def refresh_token(self, page: Page, account: CrawlerAccount) -> Dict[str, any]:
        """执行Token刷新流程"""
        try:
            # 获取当前Token作为对比基准
            old_token = account.token
            
            # 1. 访问平台主页
            homepage_url = self.get_homepage_url()
            print(f"访问{self.platform_code}主页...")
            
            try:
                page.goto(homepage_url, timeout=60000)
                print("页面加载完成，等待DOM加载...")
                page.wait_for_load_state("domcontentloaded", timeout=30000)
                print("DOM加载完成，等待3秒...")
                page.wait_for_timeout(3000)
                print("页面加载完成，开始检测Token...")
            except Exception as e:
                print(f"页面加载失败: {str(e)}")
                return {"success": False, "message": f"页面加载失败: {str(e)}"}

            # 2. 等待Token刷新
            refresh_result = self.wait_for_token_refresh(page, old_token)
            if not refresh_result["success"]:
                return refresh_result

            # 3. 提取Cookie和Token
            extract_result = self.extract_cookies_and_token(page)
            if not extract_result["success"]:
                return extract_result

            return {
                "success": True,
                "message": f"{self.platform_code} Token获取成功",
                "data": {
                    "token": extract_result["token"],
                    "token_expires_at": extract_result["token_expires_at"].isoformat() if extract_result["token_expires_at"] else None,
                    "cookie": extract_result["cookie"],
                    "user_agent": extract_result["user_agent"],
                    "cookie_count": extract_result["cookie_count"]
                }
            }

        except Exception as e:
            print(f"{self.platform_code} Token获取失败: {str(e)}")
            return {"success": False, "message": f"{self.platform_code} Token获取失败: {str(e)}"}
