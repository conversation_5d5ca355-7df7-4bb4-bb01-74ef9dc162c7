"""
平台适配器模块

提供各个电商平台的爬虫实现
"""

# 导入可用的爬虫
try:
    from .taobao_correct import TaobaoCorrectCrawler
    from .alibaba_1688 import Alibaba1688Crawler
    __all__ = ['TaobaoCorrectCrawler', 'Alibaba1688Crawler']

    # 平台爬虫映射
    PLATFORM_CRAWLERS = {
        'taobao': TaobaoCorrectCrawler,
        '1688': Alibaba1688Crawler,
    }
    print("✅ 成功导入淘宝和1688爬虫")
except ImportError as e:
    print(f"⚠️ 导入爬虫失败: {e}")
    __all__ = []
    PLATFORM_CRAWLERS = {}


def get_crawler_class(platform: str):
    """根据平台名称获取爬虫类"""
    return PLATFORM_CRAWLERS.get(platform.lower())
