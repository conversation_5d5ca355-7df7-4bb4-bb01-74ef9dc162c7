#!/usr/bin/env python3
"""
启动后端服务的诊断脚本
"""

import sys
import os

# 添加backend目录到Python路径
backend_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, backend_dir)

def test_imports():
    """测试导入"""
    print("🔍 测试导入...")
    
    try:
        print("  - 导入数据库模块...")
        from app.database import engine
        print("  ✅ 数据库模块导入成功")
        
        print("  - 导入模型...")
        from app.models import CrawlerAccount, Platform
        print("  ✅ 模型导入成功")
        
        print("  - 导入主应用...")
        from app.main import app
        print("  ✅ 主应用导入成功")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 导入失败: {e}")
        import traceback
        print(f"  详细错误:\n{traceback.format_exc()}")
        return False

def test_database():
    """测试数据库连接"""
    print("\n🗄️ 测试数据库连接...")
    
    try:
        from app.database import engine
        from sqlalchemy import text
        
        with engine.connect() as connection:
            result = connection.execute(text("SELECT 1"))
            print("  ✅ 数据库连接正常")
            return True
            
    except Exception as e:
        print(f"  ❌ 数据库连接失败: {e}")
        return False

def start_server():
    """启动服务器"""
    print("\n🚀 启动服务器...")
    
    try:
        import uvicorn
        from app.main import app
        
        print("  - 配置服务器...")
        config = uvicorn.Config(
            app=app,
            host="0.0.0.0",
            port=8000,
            reload=False,  # 禁用reload避免问题
            log_level="info"
        )
        
        print("  - 启动服务器...")
        server = uvicorn.Server(config)
        
        print("  ✅ 服务器启动成功")
        print("  📋 访问地址: http://localhost:8000")
        print("  📋 API文档: http://localhost:8000/docs")
        print("  📋 按 Ctrl+C 停止服务")
        
        server.run()
        
    except KeyboardInterrupt:
        print("\n  🛑 服务器已停止")
    except Exception as e:
        print(f"  ❌ 服务器启动失败: {e}")
        import traceback
        print(f"  详细错误:\n{traceback.format_exc()}")

def main():
    """主函数"""
    print("🏊‍♂️ 爬虫池管理系统后端启动诊断")
    print("=" * 50)
    
    # 1. 测试导入
    if not test_imports():
        print("❌ 导入测试失败，无法启动服务")
        return False
    
    # 2. 测试数据库
    if not test_database():
        print("❌ 数据库测试失败，无法启动服务")
        return False
    
    # 3. 启动服务器
    start_server()
    
    return True

if __name__ == "__main__":
    main()
