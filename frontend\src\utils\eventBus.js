import { ref } from 'vue'

// 创建一个响应式的事件总线
const eventBus = ref({})

// 事件监听器存储
const listeners = new Map()

export const useEventBus = () => {
  // 发送事件
  const emit = (eventName, data) => {
    console.log(`[EventBus] 发送事件: ${eventName}`, data)
    
    if (listeners.has(eventName)) {
      const eventListeners = listeners.get(eventName)
      eventListeners.forEach(callback => {
        try {
          callback(data)
        } catch (error) {
          console.error(`[EventBus] 事件处理器错误 (${eventName}):`, error)
        }
      })
    }
  }

  // 监听事件
  const on = (eventName, callback) => {
    if (!listeners.has(eventName)) {
      listeners.set(eventName, new Set())
    }
    listeners.get(eventName).add(callback)
    
    console.log(`[EventBus] 注册监听器: ${eventName}`)
    
    // 返回取消监听的函数
    return () => {
      const eventListeners = listeners.get(eventName)
      if (eventListeners) {
        eventListeners.delete(callback)
        if (eventListeners.size === 0) {
          listeners.delete(eventName)
        }
      }
    }
  }

  // 取消监听
  const off = (eventName, callback) => {
    if (listeners.has(eventName)) {
      const eventListeners = listeners.get(eventName)
      eventListeners.delete(callback)
      if (eventListeners.size === 0) {
        listeners.delete(eventName)
      }
    }
  }

  // 清除所有监听器
  const clear = () => {
    listeners.clear()
  }

  return {
    emit,
    on,
    off,
    clear
  }
}

// 预定义的事件类型
export const EVENTS = {
  OPEN_TAB: 'openTab',
  CLOSE_TAB: 'closeTab',
  SWITCH_TAB: 'switchTab'
}
