import { createRouter, createWebHistory } from 'vue-router'

const routes = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('../views/Login.vue')
  },
  {
    path: '/',
    name: 'Layout',
    component: () => import('../views/Layout.vue')
  },
  // 特殊路由用于外部链接访问
  {
    path: '/product-detail',
    name: 'ProductDetailDirect',
    component: () => import('../views/Layout.vue'),
    meta: { openTab: '/product-detail' }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach((to, _from, next) => {
  const token = localStorage.getItem('token')

  // 如果访问登录页面
  if (to.path === '/login') {
    if (token) {
      // 已登录，跳转到首页
      next('/')
    } else {
      // 未登录，允许访问登录页
      next()
    }
    return
  }

  // 访问其他页面需要登录
  if (!token) {
    next('/login')
  } else {
    next()
  }
})

export default router
