#!/usr/bin/env python3
"""
预置Token配置
为上游系统提供预置的API访问Token，避免频繁登录
"""
import os
import jwt
from datetime import datetime, timedelta
from typing import Dict, List
import logging

logger = logging.getLogger(__name__)

# JWT配置
SECRET_KEY = os.getenv("JWT_SECRET_KEY", "your-secret-key-change-in-production")
ALGORITHM = "HS256"

class PresetTokenManager:
    """预置Token管理器"""
    
    def __init__(self):
        # 预置Token配置
        self.preset_tokens = {
            # 上游系统1 - 长期有效Token
            "upstream_system_1": {
                "description": "上游系统1专用Token",
                "username": "upstream_system_1",
                "expires_days": 365,  # 1年有效期
                "permissions": ["search", "detail", "enhanced_detail"]
            },
            
            # 上游系统2 - 中期有效Token
            "upstream_system_2": {
                "description": "上游系统2专用Token", 
                "username": "upstream_system_2",
                "expires_days": 90,   # 3个月有效期
                "permissions": ["search", "detail"]
            },
            
            # 测试系统 - 短期有效Token
            "test_system": {
                "description": "测试系统专用Token",
                "username": "test_system", 
                "expires_days": 30,   # 1个月有效期
                "permissions": ["search", "detail", "enhanced_detail"]
            },
            
            # 开发环境 - 长期有效Token
            "dev_system": {
                "description": "开发环境专用Token",
                "username": "dev_system",
                "expires_days": 180,  # 6个月有效期
                "permissions": ["search", "detail", "enhanced_detail"]
            }
        }
        
        # 生成所有预置Token
        self.generated_tokens = self._generate_all_tokens()
        logger.info(f"预置Token管理器初始化完成，生成了 {len(self.generated_tokens)} 个Token")
    
    def _generate_token(self, username: str, expires_days: int) -> str:
        """生成Token"""
        expire = datetime.utcnow() + timedelta(days=expires_days)
        to_encode = {
            "sub": username,
            "exp": expire,
            "iat": datetime.utcnow(),
            "type": "preset"  # 标记为预置Token
        }
        encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
        return encoded_jwt
    
    def _generate_all_tokens(self) -> Dict[str, Dict]:
        """生成所有预置Token"""
        tokens = {}
        
        for token_id, config in self.preset_tokens.items():
            token = self._generate_token(config["username"], config["expires_days"])
            
            tokens[token_id] = {
                "token": token,
                "username": config["username"],
                "description": config["description"],
                "expires_days": config["expires_days"],
                "permissions": config["permissions"],
                "created_at": datetime.utcnow().isoformat(),
                "expires_at": (datetime.utcnow() + timedelta(days=config["expires_days"])).isoformat()
            }
        
        return tokens
    
    def get_token(self, token_id: str) -> str:
        """获取指定ID的Token"""
        if token_id in self.generated_tokens:
            return self.generated_tokens[token_id]["token"]
        return None
    
    def get_all_tokens(self) -> Dict[str, Dict]:
        """获取所有Token信息"""
        return self.generated_tokens
    
    def verify_preset_token(self, token: str) -> str:
        """验证预置Token并返回用户名"""
        try:
            payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
            username = payload.get("sub")
            token_type = payload.get("type")
            
            # 检查是否为预置Token
            if token_type == "preset" and username:
                return username
            
            return None
        except jwt.PyJWTError:
            return None
    
    def is_preset_token(self, token: str) -> bool:
        """检查是否为预置Token"""
        try:
            payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
            return payload.get("type") == "preset"
        except jwt.PyJWTError:
            return False
    
    def get_token_info(self, token: str) -> Dict:
        """获取Token详细信息"""
        try:
            payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
            username = payload.get("sub")
            
            # 查找对应的Token配置
            for token_id, token_info in self.generated_tokens.items():
                if token_info["username"] == username and token_info["token"] == token:
                    return {
                        "token_id": token_id,
                        "username": username,
                        "description": token_info["description"],
                        "permissions": token_info["permissions"],
                        "expires_at": token_info["expires_at"],
                        "is_preset": True
                    }
            
            return None
        except jwt.PyJWTError:
            return None

# 全局预置Token管理器实例
preset_token_manager = PresetTokenManager()

def get_preset_token_manager():
    """获取预置Token管理器实例"""
    return preset_token_manager
