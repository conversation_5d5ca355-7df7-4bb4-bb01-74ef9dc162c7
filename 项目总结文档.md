# AqentCrawler 项目总结文档

## 项目概述

AqentCrawler 是一个专为跨境电商代购系统设计的智能爬虫子系统，支持多个中国主流电商平台的商品搜索和详情获取功能。系统采用 FastAPI + Vue3 + MySQL + Redis 的技术架构，提供标准化的API接口供上游代购系统调用。

## 技术架构

### 后端技术栈
- **框架**: FastAPI (Python 3.8+)
- **数据库**: MySQL 8.0
- **缓存**: Redis 6.0+
- **ORM**: SQLAlchemy
- **认证**: JWT Token
- **爬虫**: Playwright + Chrome
- **异步**: asyncio

### 前端技术栈
- **框架**: Vue 3 + Composition API
- **UI库**: Naive UI
- **路由**: Vue Router 4
- **构建工具**: Vite
- **状态管理**: 自定义事件总线
- **界面**: TAB多页面系统

### 浏览器扩展
- **平台**: Chrome Extension Manifest V3
- **功能**: 淘宝配置提取、Cookie管理

## 核心功能模块

### 1. 上游API接口
为代购系统提供标准化的商品搜索和详情获取接口：

#### 商品搜索接口
- **路径**: `POST /api/v1/upstream/search`
- **功能**: 多平台商品搜索，支持关键词、价格筛选、排序
- **支持平台**: 淘宝、天猫、1688 (京东、拼多多开发中)
- **多语言**: 支持8种语言翻译

#### 商品详情接口
- **路径**: `POST /api/v1/upstream/detail`
- **功能**: 获取商品详细信息、SKU、属性、描述
- **特点**: 支持部分成功响应，容错性强

### 2. 爬虫管理系统
- **账号池管理**: 支持多平台账号配置、登录状态监控
- **代理池管理**: 代理IP管理、健康检查、负载均衡
- **爬虫池管理**: 智能调度、负载均衡、故障转移
- **自动刷新**: Token自动刷新、账号状态监控

### 3. 管理后台
- **系统概览**: 实时统计、性能监控
- **平台管理**: 动态平台配置
- **爬虫配置**: 账号管理、登录测试、Token刷新
- **代理管理**: 代理池配置、测试、统计
- **调用日志**: API调用记录、性能分析
- **人工验证**: 验证码处理、人工干预

### 4. 浏览器扩展
- **配置提取**: 自动提取淘宝登录配置
- **Cookie管理**: 实时同步登录状态
- **状态监控**: 页面状态检测

## 数据库设计

### 核心表结构
1. **platforms**: 平台配置表
2. **crawler_accounts**: 爬虫账号表
3. **proxy_pools**: 代理池表
4. **api_call_logs**: API调用日志表
5. **crawler_pool_***: 爬虫池相关表

### 数据特点
- 避免外键约束，提高性能
- 支持动态配置，无需重启
- 完整的日志记录，便于分析

## 部署架构

### 开发环境
```
Frontend (Vue3) -> Backend (FastAPI) -> MySQL + Redis
     ↓                    ↓
Chrome Extension    Browser Automation
```

### 生产环境建议
```
Load Balancer -> Multiple FastAPI Instances -> MySQL Cluster + Redis Cluster
                        ↓
                 Proxy Pool + Account Pool
```

## API接口规范

### 统一响应格式
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {...}
}
```

### 认证方式
- Bearer Token (JWT)
- 预置Token认证
- 可选认证模式

### 错误处理
- 标准HTTP状态码
- 详细错误信息
- 部分成功支持

## 安全特性

### 数据安全
- 密码字段加密存储
- 敏感配置环境变量
- Token过期机制

### 反爬虫对策
- 代理IP轮换
- 请求频率控制
- User-Agent随机化
- Cookie池管理

### 系统安全
- CORS配置
- 请求限流
- 异常监控

## 性能优化

### 缓存策略
- Redis缓存商品信息
- 可配置缓存时间
- 智能缓存更新

### 并发处理
- 异步请求处理
- 连接池管理
- 资源复用

### 监控告警
- 实时性能监控
- 错误率统计
- 自动故障恢复

## 项目文件结构

```
AqentCrawler/
├── backend/                 # 后端服务
│   ├── app/                # 应用代码
│   │   ├── api/           # API接口
│   │   ├── crawler/       # 爬虫模块
│   │   ├── models/        # 数据模型
│   │   ├── routers/       # 路由模块
│   │   ├── services/      # 业务服务
│   │   └── utils/         # 工具函数
│   ├── docs/              # 文档
│   ├── migrations/        # 数据库迁移
│   └── requirements.txt   # 依赖包
├── frontend/               # 前端应用
│   ├── src/
│   │   ├── api/          # API调用
│   │   ├── components/   # 组件
│   │   ├── composables/  # 组合函数
│   │   ├── router/       # 路由
│   │   ├── utils/        # 工具
│   │   └── views/        # 页面
│   └── package.json      # 依赖配置
├── chrome-extension/       # 浏览器扩展
├── docs/                  # 项目文档
└── scripts/               # 脚本工具
```

## 已清理的文件

### 测试文件 (已删除)
- 所有 `test_*.py` 文件 (33个)
- 临时测试HTML文件
- 调试用的临时文件

### 迁移脚本 (已删除)
- 数据库修复脚本
- 字段添加脚本
- 一次性迁移工具

### 临时文件 (已删除)
- temp/ 目录下的调试文件
- 响应示例文件
- 开发过程中的测试代码

## 配置要求

### 环境变量
```env
# 数据库配置
DATABASE_URL=mysql://root:password@host:port/database
REDIS_URL=redis://host:port/db

# 加密密钥
ENCRYPTION_KEY=your-encryption-key

# Chrome路径
CHROME_PATH=C:\Program Files\Google\Chrome\Application\chrome.exe

# 代理配置
PROXY_SERVER=your-proxy-server:port
```

### 系统要求
- Python 3.8+
- Node.js 16+
- MySQL 8.0+
- Redis 6.0+
- Chrome 浏览器

## 使用指南

### 启动服务
```bash
# 后端
cd backend
pip install -r requirements.txt
python start_server.py

# 前端
cd frontend
npm install
npm run dev
```

### 基本配置
1. 配置数据库连接
2. 添加平台信息
3. 配置爬虫账号
4. 设置代理池
5. 测试API接口

## 扩展性设计

### 平台扩展
- 插件化平台支持
- 统一爬虫接口
- 配置化规则引擎

### 功能扩展
- 多语言翻译服务
- 图片处理服务
- 价格监控功能

### 性能扩展
- 水平扩展支持
- 微服务架构就绪
- 容器化部署

## 维护建议

### 日常维护
- 定期清理日志
- 监控账号状态
- 更新代理池
- 检查系统性能

### 故障处理
- 查看错误日志
- 检查网络连接
- 验证账号状态
- 重启相关服务

### 版本升级
- 备份数据库
- 测试新功能
- 灰度发布
- 监控稳定性

## 技术支持

如需技术支持或有问题反馈，请联系开发团队。

---

**版本**: v1.0.0  
**更新时间**: 2025-01-20  
**文档状态**: 最新
