<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>淘宝配置提取器 - 设置</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #ff6600;
        }
        
        .header h1 {
            color: #ff6600;
            margin: 0;
            font-size: 24px;
        }
        
        .header p {
            color: #666;
            margin: 10px 0 0 0;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        
        .form-group input[type="text"],
        .form-group input[type="url"],
        .form-group input[type="number"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
        
        .form-group input[type="checkbox"] {
            margin-right: 8px;
        }
        
        .form-group .help-text {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
        
        .buttons {
            display: flex;
            gap: 10px;
            margin-top: 30px;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            transition: background-color 0.3s;
        }
        
        .btn-primary {
            background-color: #ff6600;
            color: white;
            flex: 1;
        }
        
        .btn-primary:hover {
            background-color: #e55a00;
        }
        
        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background-color: #545b62;
        }
        
        .status {
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 20px;
            text-align: center;
            font-weight: bold;
        }
        
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .backend-status {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-top: 10px;
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
        }
        
        .status-indicator.online {
            background-color: #28a745;
        }
        
        .status-indicator.offline {
            background-color: #dc3545;
        }
        
        .config-history {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
        }
        
        .config-history h3 {
            color: #333;
            margin-bottom: 15px;
        }
        
        .config-item {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 10px;
            font-size: 12px;
        }
        
        .config-item .time {
            font-weight: bold;
            color: #666;
        }
        
        .config-item .username {
            color: #ff6600;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🕷️ 淘宝配置提取器</h1>
            <p>配置和管理您的爬虫设置</p>
        </div>
        
        <div id="status" style="display: none;"></div>
        
        <form id="settingsForm">
            <div class="form-group">
                <label for="backendUrl">后端服务地址</label>
                <input type="url" id="backendUrl" placeholder="http://localhost:8000" required>
                <div class="help-text">您的爬虫后端服务地址</div>
                <div class="backend-status">
                    <div id="statusIndicator" class="status-indicator offline"></div>
                    <span id="statusText">检测中...</span>
                    <button type="button" id="testConnection" class="btn btn-secondary">测试连接</button>
                </div>
            </div>
            
            <div class="form-group">
                <label>
                    <input type="checkbox" id="autoExtract">
                    启用自动提取
                </label>
                <div class="help-text">在检测到淘宝登录状态时自动提取配置</div>
            </div>
            
            <div class="form-group">
                <label for="extractInterval">自动提取间隔（分钟）</label>
                <input type="number" id="extractInterval" min="5" max="1440" value="60">
                <div class="help-text">自动检查和提取配置的时间间隔</div>
            </div>
            
            <div class="form-group">
                <label>
                    <input type="checkbox" id="notificationEnabled" checked>
                    启用通知
                </label>
                <div class="help-text">在配置提取成功或失败时显示通知</div>
            </div>
            
            <div class="buttons">
                <button type="submit" class="btn btn-primary">保存设置</button>
                <button type="button" id="resetBtn" class="btn btn-secondary">重置</button>
            </div>
        </form>
        
        <div class="config-history">
            <h3>最近提取的配置</h3>
            <div id="configHistory">
                <div class="config-item">
                    <div class="time">暂无历史记录</div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="settings.js"></script>
</body>
</html>
