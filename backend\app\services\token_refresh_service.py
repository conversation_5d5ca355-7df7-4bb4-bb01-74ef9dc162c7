"""
Token刷新服务

提供账号Token刷新功能
"""

import os
import json
import subprocess
import asyncio
from datetime import datetime
from typing import Dict, Optional

from app.database import get_db
from app.models import CrawlerAccount


class TokenRefreshService:
    """Token刷新服务"""

    def __init__(self):
        self.refresh_timeout = 300  # 5分钟超时

    async def refresh_account_token(self, account_id: int) -> Dict[str, any]:
        """刷新指定账号的Token"""
        try:
            # 获取账号信息
            db = next(get_db())
            account = db.query(CrawlerAccount).filter(CrawlerAccount.id == account_id).first()
            
            if not account:
                return {"success": False, "message": "账号不存在"}
            
            print(f"[TOKEN_REFRESH] 开始刷新Token: account_id={account_id}, username={account.username}")
            
            # 获取独立刷新脚本的路径
            script_path = os.path.join(
                os.path.dirname(__file__),
                "standalone_token_refresh.py"
            )
            
            if not os.path.exists(script_path):
                return {"success": False, "message": "Token刷新脚本不存在"}
            
            # 启动独立刷新进程
            print(f"启动独立Token刷新进程，账号ID: {account_id}")
            
            process = await asyncio.create_subprocess_exec(
                "python", script_path, str(account_id),
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                cwd=os.path.dirname(script_path)
            )
            
            try:
                # 等待进程完成
                stdout, stderr = await asyncio.wait_for(
                    process.communicate(), 
                    timeout=self.refresh_timeout
                )
                
                print(f"[TOKEN_REFRESH] 独立进程返回码: {process.returncode}")
                
                # 解码输出
                stdout_text = stdout.decode('utf-8', errors='ignore').strip()
                stderr_text = stderr.decode('utf-8', errors='ignore').strip()
                
                print(f"[TOKEN_REFRESH] stdout: {stdout_text}")
                if stderr_text:
                    print(f"[TOKEN_REFRESH] stderr: {stderr_text}")
                
                if process.returncode == 0:
                    try:
                        # 解析JSON结果
                        result = json.loads(stdout_text)
                        print(f"[TOKEN_REFRESH] 成功解析JSON结果: {result}")
                        return result
                    except json.JSONDecodeError as e:
                        print(f"[TOKEN_REFRESH] JSON解析失败: {e}")
                        return {"success": False, "message": f"结果解析失败: {str(e)}"}
                else:
                    return {"success": False, "message": f"Token刷新进程失败，返回码: {process.returncode}"}
                    
            except asyncio.TimeoutError:
                print(f"[TOKEN_REFRESH] Token刷新超时，终止进程")
                try:
                    process.terminate()
                    await process.wait()
                except:
                    pass
                return {"success": False, "message": f"Token刷新超时（{self.refresh_timeout}秒）"}
                
        except Exception as e:
            print(f"[TOKEN_REFRESH] Token刷新异常: {str(e)}")
            return {"success": False, "message": f"Token刷新异常: {str(e)}"}
        finally:
            try:
                db.close()
            except:
                pass

    def refresh_account_token_sync(self, account_id: int) -> Dict[str, any]:
        """同步版本的Token刷新"""
        try:
            # 获取账号信息
            db = next(get_db())
            account = db.query(CrawlerAccount).filter(CrawlerAccount.id == account_id).first()
            
            if not account:
                return {"success": False, "message": "账号不存在"}
            
            print(f"[TOKEN_REFRESH] 开始刷新Token: account_id={account_id}, username={account.username}")
            
            # 获取独立刷新脚本的路径
            script_path = os.path.join(
                os.path.dirname(__file__),
                "standalone_token_refresh.py"
            )
            
            if not os.path.exists(script_path):
                return {"success": False, "message": "Token刷新脚本不存在"}
            
            # 启动独立刷新进程
            print(f"启动独立Token刷新进程，账号ID: {account_id}")
            
            process = subprocess.Popen(
                ["python", script_path, str(account_id)],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                cwd=os.path.dirname(script_path)
            )

            try:
                # 等待进程完成
                stdout, stderr = process.communicate(timeout=self.refresh_timeout)

                print(f"[TOKEN_REFRESH] 独立进程返回码: {process.returncode}")

                # 安全地解码输出
                try:
                    stdout_text = stdout.decode('utf-8', errors='ignore').strip() if stdout else ""
                    stderr_text = stderr.decode('utf-8', errors='ignore').strip() if stderr else ""
                except Exception as decode_error:
                    print(f"[TOKEN_REFRESH] 输出解码失败: {decode_error}")
                    stdout_text = ""
                    stderr_text = ""

                print(f"[TOKEN_REFRESH] stdout: {stdout_text}")
                if stderr_text:
                    print(f"[TOKEN_REFRESH] stderr: {stderr_text}")

                if process.returncode == 0 and stdout_text:
                    try:
                        # 查找JSON行（最后一行应该是JSON结果）
                        lines = stdout_text.strip().split('\n')
                        json_line = None

                        # 从后往前找第一个有效的JSON行
                        for line in reversed(lines):
                            line = line.strip()
                            if line.startswith('{') and line.endswith('}'):
                                json_line = line
                                break

                        if json_line:
                            result = json.loads(json_line)
                            print(f"[TOKEN_REFRESH] 成功解析JSON结果: {result}")
                            return result
                        else:
                            print(f"[TOKEN_REFRESH] 未找到有效的JSON输出")
                            print(f"[TOKEN_REFRESH] 完整输出: {repr(stdout_text)}")
                            return {"success": False, "message": "未找到有效的JSON输出"}

                    except json.JSONDecodeError as e:
                        print(f"[TOKEN_REFRESH] JSON解析失败: {e}")
                        print(f"[TOKEN_REFRESH] 原始输出: {repr(stdout_text)}")
                        return {"success": False, "message": f"结果解析失败: {str(e)}"}
                elif process.returncode == 0 and not stdout_text:
                    # 进程成功但没有输出，可能是用户取消
                    print(f"[TOKEN_REFRESH] 进程成功完成但无输出，可能是用户取消操作")
                    return {"success": False, "message": "操作被取消", "cancelled": True}
                else:
                    return {"success": False, "message": f"Token刷新进程失败，返回码: {process.returncode}"}

            except subprocess.TimeoutExpired:
                print(f"[TOKEN_REFRESH] Token刷新超时，终止进程")
                process.terminate()
                try:
                    process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    process.kill()
                return {"success": False, "message": f"Token刷新超时（{self.refresh_timeout}秒）", "timeout": True}
                
        except Exception as e:
            print(f"[TOKEN_REFRESH] Token刷新异常: {str(e)}")
            return {"success": False, "message": f"Token刷新异常: {str(e)}"}
        finally:
            try:
                db.close()
            except:
                pass


# 创建全局服务实例
token_refresh_service = TokenRefreshService()
