# AqentCrawler 监控系统文档

## 概述

AqentCrawler 监控系统是一个自动化的账号和系统健康监控服务，负责实时监控爬虫账号状态、Token过期情况、使用统计等关键指标，确保爬虫系统的稳定运行。

## 系统架构

### 核心组件

1. **AccountMonitorService** (`app/services/account_monitor_service.py`)
   - 主监控服务，负责协调所有监控任务
   - 运行在独立的异步任务中
   - 提供监控状态查询接口

2. **ConfigService** (`app/services/config_service.py`)
   - 账号配置和使用统计管理
   - 负责更新账号使用记录
   - 提供账号选择和评分算法

3. **数据库模型** (`app/models.py`)
   - CrawlerAccount: 爬虫账号表
   - 存储账号状态、使用统计、Token信息等

## 监控功能详解

### 1. Token过期监控

**功能描述**：
- 监控账号Token的过期状态
- 提前预警即将过期的Token
- 自动标记已过期的账号

**监控逻辑**：
```python
# 检查即将过期的Token（默认提前2小时警告）
warning_time = now + timedelta(hours=self.token_warning_hours)
expiring_accounts = db.query(CrawlerAccount).filter(
    and_(
        CrawlerAccount.login_status == 'logged_in',
        CrawlerAccount.token_expires_at.isnot(None),
        CrawlerAccount.token_expires_at <= warning_time,
        CrawlerAccount.token_expires_at > now
    )
).all()

# 检查已过期的Token
expired_accounts = db.query(CrawlerAccount).filter(
    and_(
        CrawlerAccount.login_status == 'logged_in',
        CrawlerAccount.token_expires_at.isnot(None),
        CrawlerAccount.token_expires_at <= now
    )
).all()
```

**处理动作**：
- 即将过期：记录警告日志
- 已过期：更新账号状态为 `expired`，记录错误信息

### 2. 自动Token刷新

**功能描述**：
- 在Token过期前自动刷新
- 支持开关控制
- 失败重试机制

**刷新逻辑**：
```python
# 查找需要刷新的账号（提前1小时刷新）
refresh_time = now + timedelta(hours=self.auto_refresh_advance_hours)
refresh_accounts = db.query(CrawlerAccount).filter(
    and_(
        CrawlerAccount.login_status == 'logged_in',
        CrawlerAccount.auto_refresh_enabled == True,
        CrawlerAccount.token_expires_at.isnot(None),
        CrawlerAccount.token_expires_at <= refresh_time,
        CrawlerAccount.token_expires_at > now
    )
).all()
```

### 3. 账号健康检查

**功能描述**：
- 定期检查账号整体健康状态
- 统计各种状态的账号数量
- 识别长时间未使用的账号
- 检测成功率异常的账号

**检查项目**：

#### 3.1 账号状态统计
```python
total_accounts = db.query(CrawlerAccount).count()
logged_in_count = db.query(CrawlerAccount).filter(CrawlerAccount.login_status == 'logged_in').count()
expired_count = db.query(CrawlerAccount).filter(CrawlerAccount.login_status == 'expired').count()
error_count = db.query(CrawlerAccount).filter(CrawlerAccount.login_status == 'error').count()
```

#### 3.2 长时间未使用检查
```python
# 检查超过7天未使用的账号
inactive_threshold = datetime.now() - timedelta(days=7)
inactive_accounts = db.query(CrawlerAccount).filter(
    or_(
        CrawlerAccount.last_used_at.is_(None),  # 从未使用
        CrawlerAccount.last_used_at < inactive_threshold  # 超过7天未使用
    )
).count()
```

#### 3.3 成功率异常检查
```python
# 检查成功率低于50%的账号（至少有10次请求）
high_error_accounts = db.query(CrawlerAccount).filter(
    and_(
        CrawlerAccount.total_requests > 10,
        CrawlerAccount.success_rate < 50.0
    )
).all()
```

### 4. 数据清理

**功能描述**：
- 清理过期的错误状态
- 重置长期错误的账号状态
- 维护数据库性能

**清理逻辑**：
```python
# 清理超过30天的错误状态
old_threshold = datetime.now() - timedelta(days=30)
old_error_accounts = db.query(CrawlerAccount).filter(
    and_(
        CrawlerAccount.login_status == 'error',
        CrawlerAccount.updated_at < old_threshold
    )
).all()

# 重置为pending状态，清除错误信息
for account in old_error_accounts:
    account.login_status = 'pending'
    account.last_error_message = None
    account.updated_at = datetime.now()
```

## 账号使用统计

### 统计字段

| 字段名 | 类型 | 描述 |
|--------|------|------|
| `last_used_at` | DateTime | 最后使用时间 |
| `total_requests` | Integer | 总请求数 |
| `success_requests` | Integer | 成功请求数 |
| `current_requests_count` | Integer | 当前小时请求计数 |
| `success_rate` | Decimal | 成功率百分比 |
| `error_count` | Integer | 连续错误次数 |
| `last_error_message` | Text | 最后错误信息 |

### 更新机制

#### 使用开始时更新
```python
def _update_account_usage(self, account: CrawlerAccount):
    """更新账号使用统计"""
    account.last_used_at = datetime.now()
    account.current_requests_count = (account.current_requests_count or 0) + 1
    account.total_requests = (account.total_requests or 0) + 1
    self.db.commit()
```

#### 使用结束时更新
```python
def update_account_result(self, account: CrawlerAccount, success: bool, error_message: str = None):
    """更新账号使用结果"""
    if success:
        account.success_requests = (account.success_requests or 0) + 1
        account.error_count = 0  # 成功后重置错误计数
        account.last_error_message = None
    else:
        account.error_count = (account.error_count or 0) + 1
        account.last_error_message = error_message
        
        # 连续错误过多时停用账号
        if account.error_count >= 5:
            account.status = 'error'
    
    # 重新计算成功率
    if account.total_requests > 0:
        account.success_rate = (account.success_requests / account.total_requests) * 100
    
    self.db.commit()
```

## 监控配置

### 配置参数

| 参数名 | 默认值 | 描述 |
|--------|--------|------|
| `monitor_interval` | 60秒 | 监控检查间隔 |
| `token_warning_hours` | 2小时 | Token过期预警时间 |
| `auto_refresh_advance_hours` | 1小时 | 自动刷新提前时间 |
| `health_check_interval` | 300秒 | 健康检查间隔 |

### 状态定义

#### 登录状态 (login_status)
- `not_logged_in`: 未登录
- `pending`: 等待登录
- `logging_in`: 登录中
- `logged_in`: 已登录
- `login_failed`: 登录失败
- `expired`: Token已过期
- `error`: 错误状态

#### 账号状态 (status)
- `active`: 活跃
- `inactive`: 非活跃
- `suspended`: 暂停
- `error`: 错误

## API接口

### 获取监控状态
```http
GET /api/v1/crawler/monitor/status
```

**响应示例**：
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "monitor_running": true,
    "last_health_check": "2025-06-15T19:30:00",
    "account_stats": {
      "total": 5,
      "logged_in": 3,
      "expired": 1,
      "error": 1,
      "expiring_soon": 0
    },
    "settings": {
      "monitor_interval": 60,
      "token_warning_hours": 2,
      "auto_refresh_advance_hours": 1,
      "health_check_interval": 300
    }
  }
}
```

### 启动/停止监控
```http
POST /api/v1/crawler/monitor/start
POST /api/v1/crawler/monitor/stop
```

## 日志输出

### 日志级别
- `INFO`: 正常运行信息
- `WARNING`: 警告信息（Token即将过期、账号异常等）
- `ERROR`: 错误信息（监控失败、数据库错误等）

### 日志示例
```
INFO:     账号监控服务启动
INFO:     开始执行账号健康检查
INFO:     账号健康状态 - 总计: 5, 已登录: 3, 已过期: 1, 错误: 1
WARNING:  发现 1 个账号超过7天未使用
WARNING:  账号 test_user Token将在 1.5 小时后过期
ERROR:    监控周期执行异常: Database connection failed
```

## 故障排查

### 常见问题

1. **"发现X个账号超过7天未使用"但账号刚刚使用过**
   - **原因**: `last_used_at` 字段未正确更新
   - **解决**: 检查爬虫代码中是否调用了 `_update_account_usage` 方法

2. **Token自动刷新失败**
   - **原因**: 刷新服务异常或账号配置问题
   - **解决**: 检查登录服务状态和账号配置

3. **监控服务停止运行**
   - **原因**: 数据库连接异常或代码错误
   - **解决**: 检查日志中的错误信息，修复相关问题

### 调试方法

1. **查看监控状态**：
   ```bash
   curl http://localhost:8000/api/v1/crawler/monitor/status
   ```

2. **检查数据库字段**：
   ```sql
   SELECT username, last_used_at, login_status, total_requests 
   FROM crawler_accounts 
   ORDER BY last_used_at DESC;
   ```

3. **查看服务日志**：
   - 监控相关日志会包含 `[MONITOR]` 标识
   - 账号使用日志会包含 `📊` 标识

## 最佳实践

1. **定期检查监控状态**：确保监控服务正常运行
2. **关注警告日志**：及时处理Token过期和账号异常
3. **合理配置参数**：根据业务需求调整监控间隔和预警时间
4. **备份重要账号**：为关键平台配置多个备用账号
5. **监控数据库性能**：定期清理过期数据，保持系统性能

---

*文档版本: v1.0*  
*最后更新: 2025-06-15*
