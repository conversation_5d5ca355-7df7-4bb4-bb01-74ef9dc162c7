# AqentCrawler Linux部署总结

## 📋 部署文档概览

我已经为您创建了完整的Linux CentOS7部署文档和脚本，包括：

### 📚 文档文件
1. **Linux部署指南.md** - 详细的部署指南（999行）
2. **快速部署指南.md** - 简化的部署流程
3. **Linux部署总结.md** - 本文档

### 🛠️ 脚本工具
1. **scripts/package.py** - Windows打包脚本
2. **scripts/env_manager.py** - 环境管理脚本
3. **scripts/deploy_linux.sh** - Linux自动部署脚本
4. **scripts/setup_centos7.sh** - CentOS7环境准备脚本

## 🚀 部署流程总览

### 阶段1: Windows开发环境打包
```bash
# 在Windows开发环境执行
python scripts/package.py
```
**输出**: `build/aqentcrawler-YYYYMMDD-HHMMSS.zip`

### 阶段2: Linux环境准备
```bash
# 在CentOS7服务器执行（root用户）
wget setup_centos7.sh
chmod +x setup_centos7.sh
./setup_centos7.sh
```
**安装**: Python3.8, Node.js16, MySQL8.0, Redis, Nginx, Chrome

### 阶段3: 应用部署
```bash
# 上传部署包并解压
unzip aqentcrawler-YYYYMMDD-HHMMSS.zip
cd aqentcrawler-YYYYMMDD-HHMMSS

# 运行部署脚本（aqentcrawler用户）
./scripts/deploy_linux.sh
```
**完成**: 文件部署、服务配置、自动启动

### 阶段4: 配置和验证
```bash
# 编辑配置文件
vim /home/<USER>/aqentcrawler/backend/.env

# 重启服务
sudo systemctl restart aqentcrawler

# 验证部署
curl http://localhost:8000/health
```

## 🔄 环境区分机制

### 配置文件结构
```
backend/
├── .env                    # 当前环境配置
├── .env.development        # 开发环境模板
├── .env.testing           # 测试环境模板
└── .env.production        # 生产环境模板
```

### 环境切换命令
```bash
# 切换到开发环境
python scripts/env_manager.py dev

# 切换到测试环境
python scripts/env_manager.py test

# 切换到生产环境
python scripts/env_manager.py prod

# 验证当前环境
python scripts/env_manager.py validate
```

### 环境差异对比

| 配置项 | 开发环境 | 测试环境 | 生产环境 |
|--------|----------|----------|----------|
| DEBUG | true | false | false |
| LOG_LEVEL | DEBUG | INFO | WARNING |
| WORKERS | 1 | 2 | 4 |
| HOST | 127.0.0.1 | 0.0.0.0 | 0.0.0.0 |
| 数据库 | 本地开发库 | 测试服务器 | 生产服务器 |
| Redis DB | 1 | 2 | 0 |
| 缓存时间 | 短 | 中等 | 长 |

## 🖥️ Windows vs Linux 主要差异

### 1. 路径差异
| 项目 | Windows | Linux |
|------|---------|-------|
| Chrome路径 | `C:\Program Files\Google\Chrome\Application\chrome.exe` | `/usr/bin/google-chrome` |
| 用户数据目录 | `C:\AgentCrawler\Temp` | `/home/<USER>/chrome_data` |
| 配置文件 | `.\backend\.env` | `/home/<USER>/aqentcrawler/backend/.env` |

### 2. 服务管理差异
| 操作 | Windows | Linux |
|------|---------|-------|
| 启动服务 | `python start_server.py` | `systemctl start aqentcrawler` |
| 查看日志 | 控制台输出 | `journalctl -u aqentcrawler -f` |
| 进程管理 | 任务管理器 | `systemctl status aqentcrawler` |

### 3. 权限管理差异
- **Windows**: 当前用户运行，较少权限限制
- **Linux**: 专用用户(aqentcrawler)，严格权限控制

### 4. 依赖安装差异
- **Windows**: `pip install -r requirements.txt`
- **Linux**: `pip3 install --user -r requirements.txt`

## 🔧 生产环境优化要点

### 1. 性能优化
```bash
# Gunicorn配置
workers = 4  # CPU核心数 * 2
worker_class = "uvicorn.workers.UvicornWorker"
max_requests = 1000

# MySQL优化
innodb_buffer_pool_size = 1G
max_connections = 200

# Redis优化
maxmemory 512mb
maxmemory-policy allkeys-lru
```

### 2. 安全配置
```bash
# SSL证书
certbot --nginx -d your-domain.com

# 防火墙
firewall-cmd --permanent --add-service=https
firewall-cmd --reload

# 文件权限
chmod 644 .env
chown aqentcrawler:aqentcrawler -R /home/<USER>/aqentcrawler
```

### 3. 监控告警
```bash
# 健康检查（每5分钟）
*/5 * * * * /home/<USER>/aqentcrawler/scripts/health_check.sh

# 数据库备份（每天2点）
0 2 * * * /home/<USER>/aqentcrawler/scripts/backup_database.sh

# 日志清理（每周）
0 0 * * 0 find /home/<USER>/logs -name "*.log.*" -mtime +7 -delete
```

## 📊 部署验证清单

### ✅ 环境检查
- [ ] CentOS 7.x 系统
- [ ] Python 3.8+ 已安装
- [ ] Node.js 16+ 已安装
- [ ] MySQL 8.0 已安装并运行
- [ ] Redis 已安装并运行
- [ ] Nginx 已安装并运行
- [ ] Chrome 浏览器已安装

### ✅ 应用检查
- [ ] 应用文件已部署到 `/home/<USER>/aqentcrawler/`
- [ ] 配置文件 `.env` 已正确配置
- [ ] 数据库已创建并初始化
- [ ] Python依赖已安装
- [ ] systemd服务已配置

### ✅ 服务检查
- [ ] aqentcrawler 服务运行正常
- [ ] Nginx 配置正确
- [ ] API接口可访问 (`/health`, `/docs`)
- [ ] 前端页面可访问
- [ ] 防火墙端口已开放

### ✅ 功能检查
- [ ] 管理后台可登录
- [ ] 平台配置功能正常
- [ ] 爬虫账号管理正常
- [ ] 代理池管理正常
- [ ] API调用正常

## 🚨 常见问题解决

### 1. 服务启动失败
```bash
# 查看错误日志
sudo journalctl -u aqentcrawler --no-pager

# 检查配置文件
python3 -c "from app.database import test_connection; test_connection()"

# 检查端口占用
netstat -tulpn | grep :8000
```

### 2. 数据库连接失败
```bash
# 检查MySQL服务
sudo systemctl status mysqld

# 测试连接
mysql -u aqentcrawler -p aqentcrawler

# 检查用户权限
mysql -u root -p -e "SELECT User, Host FROM mysql.user WHERE User='aqentcrawler';"
```

### 3. Chrome启动失败
```bash
# 检查Chrome版本
google-chrome --version

# 测试无头模式
google-chrome --headless --no-sandbox --disable-gpu --dump-dom https://www.baidu.com

# 检查字体支持
fc-list | grep -i chinese
```

## 📈 性能监控指标

### 系统指标
- CPU使用率 < 80%
- 内存使用率 < 80%
- 磁盘使用率 < 80%
- 网络连接数 < 1000

### 应用指标
- API响应时间 < 2秒
- 错误率 < 1%
- 缓存命中率 > 80%
- 数据库连接数 < 100

### 业务指标
- 爬虫成功率 > 95%
- 账号可用率 > 90%
- 代理可用率 > 80%
- 系统可用性 > 99%

## 🔄 维护建议

### 日常维护
- 每日检查服务状态
- 每周清理日志文件
- 每月更新系统补丁
- 每季度备份数据

### 定期优化
- 数据库性能调优
- 缓存策略优化
- 代理池更新
- 账号池维护

### 安全更新
- 定期更新密码
- 监控异常访问
- 更新安全补丁
- 审计系统日志

## 📞 技术支持

如需技术支持，请提供：
1. 系统版本信息
2. 错误日志内容
3. 服务状态信息
4. 配置文件内容（脱敏）

---

**文档版本**: v1.0.0  
**创建时间**: 2025-01-20  
**适用系统**: CentOS 7.x  
**维护状态**: 最新
