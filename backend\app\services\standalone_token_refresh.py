"""
独立Token刷新脚本

使用平台特定的刷新服务，支持扩展新平台
"""

import sys
import os
import json
from datetime import datetime
from playwright.sync_api import sync_playwright

# 添加backend目录到Python路径
backend_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, backend_dir)

from app.database import get_db
from app.models import CrawlerAccount
from app.services.platform_refresh import TaobaoRefresh, Alibaba1688Refresh


# 平台刷新服务映射
PLATFORM_REFRESH_SERVICES = {
    "taobao": TaobaoRefresh,
    "1688": Alibaba1688Refresh,
    # 可以在这里添加更多平台
    # "jingdong": JingdongRefresh,
    # "pinduoduo": PinduoduoRefresh,
}


def get_platform_refresh_service(platform_code: str):
    """获取平台刷新服务"""
    service_class = PLATFORM_REFRESH_SERVICES.get(platform_code)
    if service_class:
        return service_class()
    else:
        raise ValueError(f"不支持的平台: {platform_code}")


def get_chrome_user_data_dir(platform_id: int, username: str) -> str:
    """获取Chrome用户数据目录"""
    # 从环境变量获取基础目录
    base_dir = os.getenv('CHROME_USER_DATA_DIR', 'C:\\AgentCrawler\\ChromeUserData')
    
    # 为每个平台和用户创建独立目录
    user_data_dir = os.path.join(base_dir, f"platform_{platform_id}_{username}")
    
    # 确保目录存在
    os.makedirs(user_data_dir, exist_ok=True)
    
    return user_data_dir


def update_account_in_database(account_id: int, refresh_result: dict):
    """更新数据库中的账号信息"""
    try:
        db = next(get_db())
        account = db.query(CrawlerAccount).filter(CrawlerAccount.id == account_id).first()
        
        if not account:
            return {"success": False, "message": "账号不存在"}

        if refresh_result.get("success") and refresh_result.get("data"):
            data = refresh_result["data"]

            # 更新账号信息
            account.cookie = data.get("cookie")
            account.token = data.get("token")
            account.user_agent = data.get("user_agent")
            account.login_status = 'logged_in'  # 刷新成功后设置为已登录状态

            # 处理token过期时间
            if data.get("token_expires_at"):
                try:
                    account.token_expires_at = datetime.fromisoformat(data["token_expires_at"])
                except:
                    pass

            account.updated_at = datetime.now()
            account.last_error_message = None

            db.commit()
            print(f"账号 {account.username} Token信息已更新到数据库", file=sys.stderr)
            
        else:
            # 刷新失败，更新错误信息
            account.last_error_message = refresh_result.get("message", "Token刷新失败")
            account.updated_at = datetime.now()
            db.commit()
            print(f"账号 {account.username} Token刷新失败，错误信息已更新", file=sys.stderr)
        
        db.close()
        return {"success": True, "message": "数据库更新成功"}
        
    except Exception as e:
        print(f"更新数据库失败: {str(e)}", file=sys.stderr)
        return {"success": False, "message": f"更新数据库失败: {str(e)}"}


def main():
    """主函数"""
    if len(sys.argv) != 2:
        print("用法: python standalone_token_refresh.py <account_id>", file=sys.stderr)
        sys.exit(1)

    account_id = int(sys.argv[1])

    try:
        # 设置输出编码为UTF-8
        import io
        import codecs

        # 重定向stdout到临时变量，避免数据库连接信息污染输出
        old_stdout = sys.stdout
        sys.stdout = io.StringIO()

        # 获取数据库连接
        db = next(get_db())

        # 恢复stdout，并设置编码
        sys.stdout = old_stdout

        # 确保stderr使用UTF-8编码
        if hasattr(sys.stderr, 'reconfigure'):
            sys.stderr.reconfigure(encoding='utf-8')
        elif hasattr(sys.stderr, 'buffer'):
            sys.stderr = codecs.getwriter('utf-8')(sys.stderr.buffer)
        
        # 获取账号信息
        account = db.query(CrawlerAccount).filter(CrawlerAccount.id == account_id).first()
        if not account:
            result = {"success": False, "message": "账号不存在"}
            print(json.dumps(result, ensure_ascii=False))
            sys.exit(1)

        print(f"开始刷新Token: {account.username}", file=sys.stderr)
        print(f"平台: {account.platform.code}", file=sys.stderr)
        print(f"账号ID: {account.id}", file=sys.stderr)

        # 获取平台刷新服务
        try:
            platform_refresh_service = get_platform_refresh_service(account.platform.code)
        except ValueError as e:
            result = {"success": False, "message": str(e)}
            print(json.dumps(result, ensure_ascii=False))
            sys.exit(1)

        # 获取Chrome用户数据目录
        user_data_dir = get_chrome_user_data_dir(account.platform_id, account.username)
        
        # 配置代理（如果有）
        proxy_config = None
        if account.proxy_id:
            from app.models import ProxyPool
            proxy = db.query(ProxyPool).filter(ProxyPool.id == account.proxy_id).first()
            if proxy and proxy.is_enabled and proxy.host and proxy.port:
                proxy_config = {
                    "server": f"http://{proxy.host}:{proxy.port}"
                }

                # 添加代理认证
                if proxy.username and proxy.password:
                    from app.utils.crypto import decrypt_password
                    decrypted_password = decrypt_password(proxy.password)
                    if decrypted_password:
                        proxy_config["username"] = proxy.username
                        proxy_config["password"] = decrypted_password

                print(f"使用代理: {proxy.host}:{proxy.port}", file=sys.stderr)

        # 使用Playwright启动Chrome浏览器
        print("初始化Playwright...", file=sys.stderr)
        with sync_playwright() as playwright:
            # 构建启动参数
            launch_args = {
                "user_data_dir": user_data_dir,
                "headless": False,  # 显示浏览器以便观察刷新过程
                "args": [
                    "--no-sandbox",
                    "--disable-setuid-sandbox",
                    "--disable-dev-shm-usage",
                    "--disable-blink-features=AutomationControlled",
                    "--exclude-switches=enable-automation",
                    "--disable-gpu",
                    "--disable-software-rasterizer",
                    "--no-first-run"
                ],
                "viewport": {"width": 1920, "height": 1080},
                "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
            }

            # 添加代理配置
            if proxy_config:
                launch_args["proxy"] = proxy_config

            # 使用持久化上下文，这样可以使用已有的登录状态
            context = playwright.chromium.launch_persistent_context(**launch_args)

            # 设置浏览器上下文（反检测等）
            platform_refresh_service.setup_browser_context(context)

            page = context.new_page()

            # 执行Token刷新
            print(f"开始执行{account.platform.code} Token刷新流程...", file=sys.stderr)

            try:
                result = platform_refresh_service.refresh_token(page, account)

                # 检查是否是用户取消
                if result.get("cancelled"):
                    print(f"用户关闭了浏览器", file=sys.stderr)
                    print(json.dumps(result, ensure_ascii=False))
                else:
                    # 更新数据库
                    if result.get("success"):
                        update_result = update_account_in_database(account_id, result)
                        if not update_result.get("success"):
                            result["message"] += f" (数据库更新失败: {update_result.get('message')})"

                    # 输出结果到stdout
                    print(json.dumps(result, ensure_ascii=False))

            except KeyboardInterrupt:
                # 用户中断（Ctrl+C或关闭窗口）
                print(f"用户取消了Token刷新操作", file=sys.stderr)
                result = {"success": False, "message": "操作被取消", "cancelled": True}
                print(json.dumps(result, ensure_ascii=False))
            except Exception as refresh_error:
                error_msg = str(refresh_error)
                print(f"Token刷新过程发生错误: {error_msg}", file=sys.stderr)

                # 检查是否是浏览器关闭相关的错误
                if any(keyword in error_msg.lower() for keyword in ['closed', 'disconnected', 'target', 'browser']):
                    result = {"success": False, "message": "用户关闭了浏览器", "cancelled": True}
                else:
                    result = {"success": False, "message": f"Token刷新失败: {error_msg}"}
                print(json.dumps(result, ensure_ascii=False))

            # 关闭浏览器
            try:
                # print("关闭浏览器")
                context.close() 
            except:
                pass

    except Exception as e:
        result = {"success": False, "message": f"Token刷新过程发生异常: {str(e)}"}
        print(json.dumps(result, ensure_ascii=False))
        sys.exit(1)


if __name__ == "__main__":
    main()
