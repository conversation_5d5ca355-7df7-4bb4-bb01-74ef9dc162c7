import { useEventBus, EVENTS } from '../utils/eventBus.js'

/**
 * TAB导航的组合式函数
 * 提供打开、关闭、切换TAB的功能
 */
export const useTabNavigation = () => {
  const { emit } = useEventBus()

  /**
   * 打开指定的TAB页面
   * @param {string} tabKey - TAB的key，对应路由路径
   * @param {object} params - 传递给组件的参数
   */
  const openTab = (tabKey, params = {}) => {
    console.log(`[TabNavigation] 打开TAB: ${tabKey}`, params)
    emit(EVENTS.OPEN_TAB, { key: tabKey, params })
  }

  /**
   * 关闭指定的TAB页面
   * @param {string} tabKey - TAB的key
   */
  const closeTab = (tabKey) => {
    console.log(`[TabNavigation] 关闭TAB: ${tabKey}`)
    emit(EVENTS.CLOSE_TAB, { key: tabKey })
  }

  /**
   * 切换到指定的TAB页面
   * @param {string} tabKey - TAB的key
   */
  const switchTab = (tabKey) => {
    console.log(`[TabNavigation] 切换TAB: ${tabKey}`)
    emit(EVENTS.SWITCH_TAB, { key: tabKey })
  }

  /**
   * 打开商品详情页面
   * @param {string} productUrl - 商品URL
   */
  const openProductDetail = (productUrl) => {
    openTab('/product-detail', { 
      url: productUrl 
    })
  }

  /**
   * 打开平台管理页面
   */
  const openPlatforms = () => {
    openTab('/platforms')
  }

  /**
   * 打开爬虫配置页面
   */
  const openCrawlerConfig = () => {
    openTab('/crawler-config')
  }

  /**
   * 打开爬虫池管理页面
   */
  const openCrawlerPool = () => {
    openTab('/crawler-pool')
  }

  /**
   * 打开代理管理页面
   */
  const openProxies = () => {
    openTab('/proxies')
  }

  /**
   * 打开调用日志页面
   */
  const openLogs = () => {
    openTab('/logs')
  }

  /**
   * 打开人工验证页面
   */
  const openVerification = () => {
    openTab('/verification')
  }

  /**
   * 打开爬虫测试页面
   */
  const openCrawlerTest = () => {
    openTab('/crawler-test')
  }

  /**
   * 打开验证设置页面
   */
  const openVerificationSettings = () => {
    openTab('/verification-settings')
  }

  /**
   * 打开系统概览页面
   */
  const openDashboard = () => {
    openTab('/')
  }

  return {
    openTab,
    closeTab,
    switchTab,
    openProductDetail,
    openPlatforms,
    openCrawlerConfig,
    openCrawlerPool,
    openProxies,
    openLogs,
    openVerification,
    openCrawlerTest,
    openVerificationSettings,
    openDashboard
  }
}
