# 🏊‍♂️ 爬虫池管理系统设计方案

## 📋 **系统概述**

爬虫池管理系统是一个智能的爬虫资源调度和管理平台，通过会话管理、负载均衡、反爬检测等机制，实现爬虫资源的高效利用和稳定运行。

## 🎯 **核心功能**

### **1. 会话管理 (Session Management)**
- **会话创建**: 基于客户端IP、User-Agent、平台自动创建会话
- **会话绑定**: 将会话与最优爬虫账号绑定
- **会话跟踪**: 记录请求数、成功率、错误率等指标
- **会话过期**: 自动清理过期会话，释放资源

### **2. 智能爬虫选择 (Intelligent Crawler Selection)**
- **多维度评分**: 基于成功率、健康度、负载、使用时间等计算评分
- **负载均衡**: 优先选择负载较低的爬虫账号
- **随机性**: 在高分账号中加权随机选择，避免单点过载
- **实时状态**: 考虑Token过期、冷却期、阻塞状态等

### **3. 频率控制 (Rate Limiting)**
- **多级限制**: IP级别、账号级别、会话级别、平台级别
- **时间窗口**: 滑动窗口算法控制请求频率
- **违规处理**: 自动阻塞违规IP，递增阻塞时间
- **白名单**: 支持可信IP白名单机制

### **4. 反爬检测 (Anti-Crawling Detection)**
- **多种检测**: 验证码、频率限制、IP封禁、账号封禁等
- **风险评分**: 动态计算会话和账号的风险评分
- **自动处理**: 检测到反爬时自动切换爬虫或暂停服务
- **学习机制**: 基于历史数据优化检测策略

## 🏗️ **系统架构**

### **数据库设计**

#### **1. 会话管理表 (crawler_sessions)**
```sql
- session_id: 会话唯一标识
- client_ip: 客户端IP地址
- platform_code: 平台代码
- account_id: 绑定的爬虫账号
- status: 会话状态 (active/expired/blocked/error)
- request_count: 请求总数
- success_count: 成功请求数
- error_count: 错误请求数
- risk_score: 风险评分 (0-100)
- expires_at: 会话过期时间
```

#### **2. 爬虫池调度表 (crawler_pool_schedule)**
```sql
- account_id: 爬虫账号ID
- platform_code: 平台代码
- status: 调度状态 (available/busy/cooling/blocked/maintenance)
- priority_score: 优先级评分
- load_factor: 负载因子 (0-1)
- current_sessions: 当前会话数
- max_concurrent_sessions: 最大并发会话数
- health_score: 健康度评分 (0-100)
- last_used_at: 最后使用时间
```

#### **3. 频率控制表 (crawler_rate_limits)**
```sql
- identifier: 标识符 (IP/账号/会话)
- identifier_type: 标识符类型
- platform_code: 平台代码
- max_requests: 最大请求数
- current_requests: 当前请求数
- window_start_at: 时间窗口开始
- violation_count: 违规次数
- blocked_until: 阻塞到期时间
```

### **核心算法**

#### **1. 爬虫选择算法**
```python
def calculate_crawler_score(crawler):
    score = 0.0
    
    # 成功率权重 (40%)
    success_rate = float(crawler.success_rate or 0)
    score += success_rate * 0.4
    
    # 健康度权重 (30%)
    health_score = 100.0 - min(float(crawler.error_count or 0) * 10, 50)
    score += health_score * 0.3
    
    # 负载权重 (20%)
    load_factor = float(schedule.load_factor or 0)
    load_score = (1.0 - load_factor) * 100
    score += load_score * 0.2
    
    # 最后使用时间权重 (10%)
    hours_since_last_use = (now - crawler.last_used_at).total_seconds() / 3600
    freshness_score = min(hours_since_last_use * 10, 100)
    score += freshness_score * 0.1
    
    return max(score, 0.0)
```

#### **2. 频率控制算法**
```python
def check_rate_limit(client_ip, platform_code):
    # 滑动窗口检查
    window_start = now - timedelta(minutes=1)
    
    if rate_limit.current_requests > rate_limit.max_requests:
        # 违规处理
        rate_limit.violation_count += 1
        block_duration = min(rate_limit.violation_count * 60, 3600)
        rate_limit.blocked_until = now + timedelta(seconds=block_duration)
        return False
    
    return True
```

## 🎛️ **爬虫选择策略**

### **使用同一爬虫的情况**
1. **会话绑定**: 同一会话内优先使用已绑定的爬虫
2. **IP亲和性**: 相同IP的请求倾向于使用相同爬虫
3. **平台特性**: 某些平台对账号切换敏感时保持使用同一账号
4. **负载充足**: 当前爬虫负载未满且状态良好

### **切换爬虫的情况**
1. **负载过高**: 当前爬虫并发会话数达到上限
2. **健康度下降**: 爬虫错误率过高或成功率下降
3. **Token过期**: 当前爬虫Token即将过期或已过期
4. **反爬检测**: 检测到验证码、IP封禁等反爬措施
5. **冷却期**: 爬虫进入冷却期，暂时不可用
6. **强制轮换**: 定期轮换策略，避免单点过载

### **负载均衡策略**
1. **加权轮询**: 基于爬虫评分进行加权轮询
2. **最少连接**: 优先选择当前会话数最少的爬虫
3. **响应时间**: 考虑爬虫的平均响应时间
4. **地理位置**: 根据代理IP地理位置进行分配
5. **时间分片**: 不同时间段使用不同的爬虫组合

## 📊 **监控指标**

### **系统级指标**
- 总爬虫数量和状态分布
- 平均健康度评分
- 总活跃会话数
- 每分钟总请求数
- 系统整体成功率

### **爬虫级指标**
- 个体成功率和错误率
- 平均响应时间
- 当前负载和并发数
- Token剩余时间
- 最后使用时间

### **会话级指标**
- 会话请求数和成功率
- 会话风险评分
- 会话持续时间
- 绑定爬虫变更次数

## 🔧 **配置参数**

### **系统配置**
```python
SESSION_TIMEOUT = 3600  # 会话超时时间(秒)
MAX_SESSIONS_PER_IP = 3  # 每个IP最大会话数
MAX_REQUESTS_PER_MINUTE = 60  # 每分钟最大请求数
COOLING_PERIOD = 300  # 冷却期(秒)
HEALTH_THRESHOLD = 80.0  # 健康度阈值
```

### **平台特定配置**
```python
PLATFORM_CONFIGS = {
    'taobao': {
        'max_concurrent_sessions': 5,
        'max_requests_per_minute': 60,
        'cooling_period': 300,
        'token_refresh_threshold': 300  # 5分钟
    },
    '1688': {
        'max_concurrent_sessions': 3,
        'max_requests_per_minute': 30,
        'cooling_period': 600,
        'token_refresh_threshold': 600  # 10分钟
    }
}
```

## 🚀 **部署和使用**

### **1. 数据库初始化**
```bash
# 创建爬虫池管理表
python backend/migrations/create_crawler_pool_tables.py
```

### **2. 启动服务**
```bash
# 后端服务
cd backend
python -m uvicorn app.main:app --reload

# 前端服务
cd frontend
npm run dev
```

### **3. API使用示例**
```python
# 获取爬虫池状态
GET /api/v1/crawler-pool/status

# 获取活跃会话
GET /api/v1/crawler-pool/sessions

# 更新爬虫状态
POST /api/v1/crawler-pool/schedules/{account_id}/update-status

# 重置爬虫状态
POST /api/v1/crawler-pool/reset-crawler/{account_id}
```

## 🎯 **最佳实践**

### **1. 爬虫配置**
- 合理设置每个爬虫的并发限制
- 定期检查和更新Token
- 监控爬虫健康度，及时处理异常

### **2. 频率控制**
- 根据平台特性调整请求频率
- 设置合理的违规阻塞时间
- 建立可信IP白名单

### **3. 反爬应对**
- 多样化User-Agent和请求头
- 使用代理IP池分散请求
- 实现智能重试和降级策略

### **4. 监控告警**
- 设置关键指标告警阈值
- 建立自动化故障恢复机制
- 定期分析和优化性能

## 📈 **性能优化**

### **1. 数据库优化**
- 合理设计索引，提高查询效率
- 定期清理历史数据
- 使用连接池管理数据库连接

### **2. 缓存策略**
- 缓存爬虫状态和评分
- 缓存频率限制检查结果
- 使用Redis提高响应速度

### **3. 异步处理**
- 异步记录请求日志
- 异步更新统计指标
- 异步执行清理任务

## 🔮 **未来扩展**

### **1. 机器学习优化**
- 基于历史数据预测最优爬虫选择
- 智能反爬检测和应对策略
- 动态调整系统参数

### **2. 分布式部署**
- 支持多节点部署
- 实现跨节点负载均衡
- 分布式会话管理

### **3. 高级功能**
- 爬虫性能基准测试
- 自动化爬虫账号管理
- 智能代理IP轮换

---

## 📞 **技术支持**

如有问题或建议，请联系开发团队或查看相关文档。

**系统版本**: v1.0.0  
**最后更新**: 2025-06-20
