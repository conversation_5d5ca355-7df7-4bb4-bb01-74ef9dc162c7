# 创建Chrome扩展图标指南

## 📁 需要创建的图标文件

在 `chrome-extension` 文件夹中创建 `icons` 子文件夹，然后创建以下图标文件：

```
chrome-extension/
└── icons/
    ├── icon16.png    (16x16 像素)
    ├── icon32.png    (32x32 像素)
    ├── icon48.png    (48x48 像素)
    └── icon128.png   (128x128 像素)
```

## 🎨 图标设计建议

### 设计元素
- **主色调**: 橙色 (#ff6600) - 淘宝品牌色
- **辅助色**: 白色、灰色
- **图案**: 爬虫、蜘蛛、或者淘宝相关元素

### 设计风格
- **简洁明了**: 在小尺寸下也能清晰识别
- **品牌一致**: 与淘宝色调保持一致
- **功能明确**: 体现爬虫/数据提取的功能

## 🛠️ 创建方法

### 方法一：在线图标生成器
1. 访问 [Favicon Generator](https://favicon.io/favicon-generator/)
2. 选择文字图标，输入 "TB" 或 "爬"
3. 选择橙色背景 (#ff6600)
4. 下载不同尺寸的图标

### 方法二：使用设计软件
1. **Photoshop/GIMP**:
   - 创建不同尺寸的画布
   - 使用橙色背景
   - 添加白色的蜘蛛或爬虫图标

2. **Canva**:
   - 搜索 "app icon" 模板
   - 自定义颜色和图案
   - 导出为PNG格式

### 方法三：使用现有图标
1. 从 [Icons8](https://icons8.com/) 下载蜘蛛图标
2. 使用在线工具调整尺寸和颜色
3. 保存为所需的文件名

## 📝 快速创建步骤

如果您想快速开始测试，可以：

1. **创建 icons 文件夹**:
   ```
   mkdir chrome-extension/icons
   ```

2. **下载临时图标**:
   - 从任何图标网站下载一个简单的橙色图标
   - 重命名为 icon16.png, icon32.png, icon48.png, icon128.png
   - 放入 icons 文件夹

3. **或者使用文字图标**:
   - 创建橙色背景的正方形图片
   - 在中间添加白色的 "TB" 或 "爬" 字

## ✅ 验证图标

创建完图标后：

1. 确保所有4个尺寸的图标都存在
2. 文件名完全匹配 manifest.json 中的定义
3. 图标在不同尺寸下都清晰可见
4. 颜色与淘宝品牌保持一致

## 🚀 完成后的下一步

图标创建完成后，您就可以：

1. 按照 README.md 中的步骤安装扩展
2. 在Chrome中加载扩展
3. 在淘宝页面测试配置提取功能

---

**注意**: 图标只是扩展的外观，核心功能已经完全实现。即使使用简单的临时图标，扩展也能正常工作。
