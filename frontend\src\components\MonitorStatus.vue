<template>
  <div class="monitor-status">
    <div class="monitor-header">
      <h3>🔍 账号监控状态</h3>
      <div class="monitor-controls">
        <button @click="refreshStatus" class="btn-secondary" :disabled="loading">
          {{ loading ? '刷新中...' : '🔄 刷新' }}
        </button>
        <button @click="toggleMonitor" 
                :class="monitorStatus?.monitor_running ? 'btn-danger' : 'btn-success'"
                :disabled="operationInProgress">
          {{ operationInProgress ? '操作中...' : (monitorStatus?.monitor_running ? '⏹️ 停止监控' : '▶️ 启动监控') }}
        </button>
      </div>
    </div>

    <div v-if="loading" class="loading">加载中...</div>

    <div v-else-if="error" class="error">
      ❌ {{ error }}
    </div>

    <div v-else-if="monitorStatus" class="monitor-content">
      <!-- 监控服务状态 -->
      <div class="status-card">
        <div class="status-header">
          <h4>服务状态</h4>
          <span class="status-indicator" :class="monitorStatus.monitor_running ? 'running' : 'stopped'">
            {{ monitorStatus.monitor_running ? '🟢 运行中' : '🔴 已停止' }}
          </span>
        </div>
        <div class="status-details">
          <div class="detail-item">
            <span class="label">最后健康检查:</span>
            <span class="value">{{ formatDate(monitorStatus.last_health_check) }}</span>
          </div>
          <div class="detail-item">
            <span class="label">监控间隔:</span>
            <span class="value">{{ monitorStatus.settings?.monitor_interval }}秒</span>
          </div>
          <div class="detail-item">
            <span class="label">Token警告时间:</span>
            <span class="value">{{ monitorStatus.settings?.token_warning_hours }}小时</span>
          </div>
        </div>
      </div>

      <!-- 账号统计 -->
      <div class="stats-card">
        <h4>账号统计</h4>
        <div class="stats-grid">
          <div class="stat-item">
            <div class="stat-number">{{ monitorStatus.account_stats?.total || 0 }}</div>
            <div class="stat-label">总账号数</div>
          </div>
          <div class="stat-item success">
            <div class="stat-number">{{ monitorStatus.account_stats?.logged_in || 0 }}</div>
            <div class="stat-label">已登录</div>
          </div>
          <div class="stat-item warning">
            <div class="stat-number">{{ monitorStatus.account_stats?.expiring_soon || 0 }}</div>
            <div class="stat-label">即将过期</div>
          </div>
          <div class="stat-item expired">
            <div class="stat-number">{{ monitorStatus.account_stats?.expired || 0 }}</div>
            <div class="stat-label">已过期</div>
          </div>
          <div class="stat-item error">
            <div class="stat-number">{{ monitorStatus.account_stats?.error || 0 }}</div>
            <div class="stat-label">错误状态</div>
          </div>
        </div>
      </div>

      <!-- 告警信息 -->
      <div class="alerts-card" v-if="alerts">
        <div class="alerts-header">
          <h4>⚠️ 告警信息</h4>
          <button @click="refreshAlerts" class="btn-secondary btn-sm">刷新告警</button>
        </div>
        
        <!-- 即将过期的Token -->
        <div v-if="alerts.expiring_tokens?.length > 0" class="alert-section">
          <h5>🟡 Token即将过期 ({{ alerts.expiring_tokens.length }}个)</h5>
          <div class="alert-list">
            <div v-for="item in alerts.expiring_tokens" :key="item.account_id" class="alert-item warning">
              <span class="account-info">{{ item.platform }} - {{ item.username }}</span>
              <span class="time-info">{{ item.hours_left }}小时后过期</span>
            </div>
          </div>
        </div>

        <!-- 已过期的Token -->
        <div v-if="alerts.expired_tokens?.length > 0" class="alert-section">
          <h5>🔴 Token已过期 ({{ alerts.expired_tokens.length }}个)</h5>
          <div class="alert-list">
            <div v-for="item in alerts.expired_tokens" :key="item.account_id" class="alert-item error">
              <span class="account-info">{{ item.platform }} - {{ item.username }}</span>
              <span class="time-info">已过期{{ item.hours_expired }}小时</span>
            </div>
          </div>
        </div>

        <!-- 错误状态账号 -->
        <div v-if="alerts.error_accounts?.length > 0" class="alert-section">
          <h5>❌ 错误状态账号 ({{ alerts.error_accounts.length }}个)</h5>
          <div class="alert-list">
            <div v-for="item in alerts.error_accounts" :key="item.account_id" class="alert-item error">
              <span class="account-info">{{ item.platform }} - {{ item.username }}</span>
              <span class="error-info">{{ item.error_message || '未知错误' }}</span>
            </div>
          </div>
        </div>

        <!-- 成功率低的账号 -->
        <div v-if="alerts.low_success_accounts?.length > 0" class="alert-section">
          <h5>📉 成功率低的账号 ({{ alerts.low_success_accounts.length }}个)</h5>
          <div class="alert-list">
            <div v-for="item in alerts.low_success_accounts" :key="item.account_id" class="alert-item warning">
              <span class="account-info">{{ item.platform }} - {{ item.username }}</span>
              <span class="success-rate">成功率: {{ item.success_rate }}%</span>
            </div>
          </div>
        </div>

        <div v-if="!hasAlerts" class="no-alerts">
          ✅ 暂无告警信息
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import api from '../api'

// 响应式数据
const loading = ref(false)
const error = ref('')
const operationInProgress = ref(false)
const monitorStatus = ref(null)
const alerts = ref(null)

// 计算属性
const hasAlerts = computed(() => {
  if (!alerts.value) return false
  return (alerts.value.expiring_tokens?.length > 0) ||
         (alerts.value.expired_tokens?.length > 0) ||
         (alerts.value.error_accounts?.length > 0) ||
         (alerts.value.low_success_accounts?.length > 0)
})

// 格式化日期
const formatDate = (dateStr) => {
  if (!dateStr) return '从未执行'
  return new Date(dateStr).toLocaleString('zh-CN')
}

// 刷新监控状态
const refreshStatus = async () => {
  loading.value = true
  error.value = ''
  
  try {
    const response = await api.crawlerConfig.getMonitorStatus()
    if (response.code === 200) {
      monitorStatus.value = response.data
    } else {
      error.value = response.message || '获取监控状态失败'
    }
  } catch (err) {
    console.error('获取监控状态失败:', err)
    error.value = '获取监控状态失败'
  } finally {
    loading.value = false
  }
}

// 刷新告警信息
const refreshAlerts = async () => {
  try {
    const response = await api.crawlerConfig.getMonitorAlerts()
    if (response.code === 200) {
      alerts.value = response.data
    }
  } catch (err) {
    console.error('获取告警信息失败:', err)
  }
}

// 切换监控状态
const toggleMonitor = async () => {
  operationInProgress.value = true
  
  try {
    const isRunning = monitorStatus.value?.monitor_running
    const response = isRunning 
      ? await api.crawlerConfig.stopMonitor()
      : await api.crawlerConfig.startMonitor()
    
    if (response.code === 200) {
      // 延迟刷新状态，给服务时间启动/停止
      setTimeout(() => {
        refreshStatus()
      }, 1000)
    } else {
      error.value = response.message || '操作失败'
    }
  } catch (err) {
    console.error('切换监控状态失败:', err)
    error.value = '操作失败'
  } finally {
    operationInProgress.value = false
  }
}

// 页面加载时获取数据
onMounted(() => {
  refreshStatus()
  refreshAlerts()
  
  // 设置定时刷新
  setInterval(() => {
    if (!loading.value && !operationInProgress.value) {
      refreshStatus()
      refreshAlerts()
    }
  }, 30000) // 每30秒刷新一次
})
</script>

<style scoped>
.monitor-status {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  padding: 20px;
  margin-bottom: 20px;
}

.monitor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #eee;
}

.monitor-header h3 {
  margin: 0;
  color: #333;
}

.monitor-controls {
  display: flex;
  gap: 12px;
}

.loading, .error {
  text-align: center;
  padding: 40px;
  color: #666;
}

.error {
  color: #ff4d4f;
}

.monitor-content {
  display: grid;
  gap: 20px;
}

.status-card, .stats-card, .alerts-card {
  border: 1px solid #eee;
  border-radius: 6px;
  padding: 16px;
}

.status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.status-indicator.running {
  color: #52c41a;
}

.status-indicator.stopped {
  color: #ff4d4f;
}

.status-details {
  display: grid;
  gap: 8px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
}

.label {
  color: #666;
}

.value {
  font-weight: 500;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 16px;
}

.stat-item {
  text-align: center;
  padding: 12px;
  border-radius: 6px;
  background: #f5f5f5;
}

.stat-item.success {
  background: #f6ffed;
  color: #52c41a;
}

.stat-item.warning {
  background: #fff7e6;
  color: #fa8c16;
}

.stat-item.expired {
  background: #fff2f0;
  color: #ff4d4f;
}

.stat-item.error {
  background: #fff2f0;
  color: #ff4d4f;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  opacity: 0.8;
}

.alerts-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.alert-section {
  margin-bottom: 16px;
}

.alert-section h5 {
  margin: 0 0 8px 0;
  font-size: 14px;
}

.alert-list {
  display: grid;
  gap: 8px;
}

.alert-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 14px;
}

.alert-item.warning {
  background: #fff7e6;
  border-left: 3px solid #fa8c16;
}

.alert-item.error {
  background: #fff2f0;
  border-left: 3px solid #ff4d4f;
}

.account-info {
  font-weight: 500;
}

.time-info, .error-info, .success-rate {
  font-size: 12px;
  opacity: 0.8;
}

.no-alerts {
  text-align: center;
  color: #52c41a;
  padding: 20px;
}

.btn-sm {
  padding: 4px 8px;
  font-size: 12px;
}
</style>
