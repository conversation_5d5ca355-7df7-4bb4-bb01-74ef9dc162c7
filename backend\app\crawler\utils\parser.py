"""
数据解析工具

提供商品信息解析和数据清洗功能
"""

import re
import json
from typing import Dict, List, Optional, Any
from urllib.parse import urljoin, urlparse
from bs4 import BeautifulSoup


class ProductParser:
    """商品数据解析器"""
    
    @staticmethod
    def clean_text(text: str) -> str:
        """清理文本"""
        if not text:
            return ""
        
        # 移除多余空白字符
        text = re.sub(r'\s+', ' ', text.strip())
        
        # 移除特殊字符
        text = re.sub(r'[^\w\s\u4e00-\u9fff\-\.\,\(\)\[\]\/]', '', text)
        
        return text
    
    @staticmethod
    def extract_price(price_text: str) -> Optional[float]:
        """提取价格"""
        if not price_text:
            return None
        
        # 移除货币符号和其他字符，只保留数字和小数点
        price_str = re.sub(r'[^\d\.]', '', price_text)
        
        try:
            return float(price_str)
        except (ValueError, TypeError):
            return None
    
    @staticmethod
    def extract_number(text: str) -> Optional[int]:
        """提取数字"""
        if not text:
            return None
        
        # 提取数字
        numbers = re.findall(r'\d+', text)
        if numbers:
            try:
                return int(numbers[0])
            except ValueError:
                pass
        
        return None
    
    @staticmethod
    def normalize_url(url: str, base_url: str) -> str:
        """标准化URL"""
        if not url:
            return ""
        
        # 如果是相对URL，转换为绝对URL
        if url.startswith('//'):
            url = 'https:' + url
        elif url.startswith('/'):
            url = urljoin(base_url, url)
        elif not url.startswith('http'):
            url = urljoin(base_url, url)
        
        return url
    
    @staticmethod
    def extract_image_url(img_element: str, base_url: str) -> str:
        """提取图片URL"""
        if not img_element:
            return ""
        
        # 尝试从不同属性中提取图片URL
        img_attrs = ['src', 'data-src', 'data-original', 'data-lazy-src']
        
        soup = BeautifulSoup(img_element, 'html.parser')
        img_tag = soup.find('img')
        
        if img_tag:
            for attr in img_attrs:
                url = img_tag.get(attr)
                if url:
                    return ProductParser.normalize_url(url, base_url)
        
        return ""
    
    @staticmethod
    def parse_rating(rating_text: str) -> Optional[float]:
        """解析评分"""
        if not rating_text:
            return None
        
        # 提取评分数字
        rating_match = re.search(r'(\d+\.?\d*)', rating_text)
        if rating_match:
            try:
                rating = float(rating_match.group(1))
                # 确保评分在合理范围内
                if 0 <= rating <= 5:
                    return rating
            except ValueError:
                pass
        
        return None
    
    @staticmethod
    def parse_sales(sales_text: str) -> Optional[str]:
        """解析销量"""
        if not sales_text:
            return None
        
        # 清理销量文本
        sales_text = ProductParser.clean_text(sales_text)
        
        # 提取销量数字和单位
        sales_patterns = [
            r'(\d+\.?\d*[万千百十]?)\s*[件个笔台套]',
            r'(\d+\.?\d*[万千百十]?)\s*销量',
            r'(\d+\.?\d*[万千百十]?)\s*已售',
            r'(\d+\.?\d*[万千百十]?)\s*人付款',
            r'(\d+\.?\d*[万千百十]?)'
        ]
        
        for pattern in sales_patterns:
            match = re.search(pattern, sales_text)
            if match:
                return match.group(1)
        
        return sales_text if sales_text else None
    
    @staticmethod
    def extract_shop_name(shop_element: str) -> Optional[str]:
        """提取店铺名称"""
        if not shop_element:
            return None
        
        soup = BeautifulSoup(shop_element, 'html.parser')
        
        # 尝试从不同标签中提取店铺名称
        shop_selectors = [
            'a[title]',
            '.shop-name',
            '.store-name',
            '.seller-name',
            'a'
        ]
        
        for selector in shop_selectors:
            element = soup.select_one(selector)
            if element:
                shop_name = element.get('title') or element.get_text()
                if shop_name:
                    return ProductParser.clean_text(shop_name)
        
        return None
    
    @staticmethod
    def parse_location(location_text: str) -> Optional[str]:
        """解析发货地"""
        if not location_text:
            return None
        
        location_text = ProductParser.clean_text(location_text)
        
        # 提取地区信息
        location_patterns = [
            r'([^发货]*?)发货',
            r'来自\s*([^来自]*)',
            r'([^地区]*?)地区',
            r'([^省市区]*?[省市区])'
        ]
        
        for pattern in location_patterns:
            match = re.search(pattern, location_text)
            if match:
                location = match.group(1).strip()
                if location:
                    return location
        
        return location_text if location_text else None
    
    @staticmethod
    def validate_product_data(product_data: Dict[str, Any]) -> bool:
        """验证商品数据完整性"""
        required_fields = ['title', 'price', 'product_url']
        
        for field in required_fields:
            if not product_data.get(field):
                return False
        
        # 验证价格格式
        price = product_data.get('price')
        if isinstance(price, str):
            if not ProductParser.extract_price(price):
                return False
        
        # 验证URL格式
        url = product_data.get('product_url')
        if url:
            parsed = urlparse(url)
            if not parsed.scheme or not parsed.netloc:
                return False
        
        return True
    
    @staticmethod
    def standardize_product_data(product_data: Dict[str, Any], platform: str, base_url: str) -> Dict[str, Any]:
        """标准化商品数据"""
        standardized = {
            'title': ProductParser.clean_text(product_data.get('title', '')),
            'price': product_data.get('price', ''),
            'image_url': ProductParser.normalize_url(product_data.get('image_url', ''), base_url),
            'product_url': ProductParser.normalize_url(product_data.get('product_url', ''), base_url),
            'platform': platform,
            'shop_name': ProductParser.clean_text(product_data.get('shop_name', '')),
            'rating': ProductParser.parse_rating(product_data.get('rating', '')),
            'sales': ProductParser.parse_sales(product_data.get('sales', '')),
            'location': ProductParser.parse_location(product_data.get('location', '')),
            'extra_info': product_data.get('extra_info', {})
        }
        
        return standardized
