# AqentCrawler API 对接文档

## 概述

AqentCrawler 是一个智能代购爬虫系统，提供商品搜索和详情获取功能。本文档描述了系统的API接口规范。

## 基础信息

- **基础URL**: `http://your-domain:8000`
- **认证方式**: Bearer <PERSON> (JWT)
- **响应格式**: JSON
- **字符编码**: UTF-8

## 认证系统

### 1. 用户登录

**接口**: `POST /api/v1/auth/login`

**请求参数**:
```json
{
    "username": "admin",
    "password": "admin123"
}
```

**响应格式**:
```json
{
    "code": 200,
    "message": "登录成功",
    "data": {
        "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
        "username": "admin",
        "expires_in": 86400
    }
}
```

### 2. 令牌验证

**接口**: `POST /api/v1/auth/verify`

**请求头**:
```
Authorization: Bearer <token>
```

**响应格式**:
```json
{
    "code": 200,
    "message": "令牌有效",
    "data": {
        "username": "admin",
        "valid": true
    }
}
```

## 商品搜索API

### 接口信息

**接口**: `POST /api/v1/search`

**认证**: 需要Bearer Token

**请求参数**:
```json
{
    "query": "手机",
    "platform": "taobao",
    "language": "zh"
}
```

**参数说明**:
- `query` (必填): 搜索关键词
- `platform` (可选): 平台代码，默认"taobao"，支持"taobao"、"jingdong"
- `language` (可选): 语言代码，默认"zh"

**响应格式**:
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "platform": "taobao",
        "query": "手机",
        "total": 48,
        "products": [
            {
                "title": "Apple iPhone 15 Pro Max",
                "price": "¥9999",
                "sales": "1000+人付款",
                "location": "广东 深圳",
                "shop_name": "Apple官方旗舰店",
                "link": "https://item.taobao.com/item.htm?id=*********",
                "image": "http://g.search2.alicdn.com/img/...",
                "platform": "taobao"
            }
        ],
        "crawl_time": **********,
        "account_used": "test_account"
    }
}
```

## 商品详情API

### 1. 标准详情接口

**接口**: `POST /api/v1/product/detail`

**认证**: 需要Bearer Token

**请求参数**:
```json
{
    "url": "https://item.taobao.com/item.htm?id=************&skuId=*************&query=泡泡玛特"
}
```

**参数说明**:
- `url` (必填): 商品链接，支持淘宝和天猫链接

**响应格式**:
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "item_id": "************",
        "title": "泡泡玛特 MOLLY 系列盲盒",
        "subtitle": "正版授权 随机款式",
        "price": "¥59",
        "original_price": "¥69",
        "images": [
            "http://g.search2.alicdn.com/img/...",
            "http://g.search2.alicdn.com/img/..."
        ],
        "main_image": "http://g.search2.alicdn.com/img/...",
        "brand": "泡泡玛特",
        "category": "玩具/模型",
        "props": [
            {"name": "材质", "value": "PVC"},
            {"name": "尺寸", "value": "约8-10cm"}
        ],
        "skus": [],
        "shop_name": "泡泡玛特官方旗舰店",
        "shop_id": "123456",
        "seller_nick": "popmart_official",
        "shop_url": "https://shop123456.taobao.com",
        "delivery": {
            "from": "上海",
            "freight": "包邮",
            "postage": "0"
        },
        "description": "商品详细描述内容...",
        "description_images": [],
        "sales": "10000+",
        "comments": "5000+",
        "url": "https://item.taobao.com/item.htm?id=************",
        "platform": "taobao",
        "crawl_time": **********
    }
}
```

### 2. 增强详情接口 (推荐上游使用)

**接口**: `POST /api/v1/product/detail-enhanced`

**认证**: 需要Bearer Token

**特点**: 
- 分别获取商品信息和描述
- 支持部分失败，不会因为一个接口失败导致全部失败
- 提供详细的错误信息

**请求参数**:
```json
{
    "url": "https://item.taobao.com/item.htm?id=************&skuId=*************&query=泡泡玛特"
}
```

**响应格式**:
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "item_id": "************",
        "platform_type": "taobao",
        "detail_success": true,
        "description_success": false,
        "detail_data": {
            "item_info": {...},
            "seller_info": {...},
            "price_info": {...},
            "delivery_info": {...},
            "raw_data": {...}
        },
        "description_data": null,
        "detail_error": null,
        "description_error": "API请求失败，状态码: 403"
    }
}
```

**状态说明**:
- `detail_success`: 商品详情获取是否成功
- `description_success`: 商品描述获取是否成功
- `detail_data`: 商品详情数据 (成功时)
- `description_data`: 商品描述数据 (成功时)
- `detail_error`: 详情获取错误信息 (失败时)
- `description_error`: 描述获取错误信息 (失败时)

## 平台支持

### 淘宝/天猫平台

**平台代码**: `taobao`

**支持的URL格式**:
- 淘宝: `https://item.taobao.com/item.htm?id=*********`
- 天猫: `https://detail.tmall.com/item.htm?id=*********`

**自动识别**: 系统会自动识别URL中的平台类型，并使用相应的API参数

### 京东平台

**平台代码**: `jingdong`

**状态**: 已支持搜索功能，详情功能开发中

## 错误处理

### 错误响应格式

```json
{
    "code": 400,
    "message": "请提供商品链接或商品ID",
    "data": null
}
```

### 常见错误码

- `400`: 请求参数错误
- `401`: 认证失败或令牌无效
- `403`: 权限不足
- `404`: 资源不存在
- `500`: 服务器内部错误

### 部分成功响应

增强详情接口支持部分成功，即使某个子接口失败也会返回可用数据：

```json
{
    "code": 200,
    "message": "部分成功：商品描述获取失败",
    "data": {
        "detail_success": true,
        "description_success": false,
        "detail_data": {...},
        "description_error": "具体错误信息"
    }
}
```

## 使用建议

### 1. 认证管理
- 登录后保存token，在后续请求中使用
- token有效期24小时，过期前重新登录
- 使用`/api/v1/auth/verify`检查token有效性

### 2. 接口选择
- **搜索**: 使用`/api/v1/search`
- **详情**: 推荐使用`/api/v1/product/detail-enhanced`，容错性更好

### 3. 错误处理
- 检查响应的`code`字段判断成功/失败
- 对于增强详情接口，检查`detail_success`和`description_success`
- 实现重试机制处理临时性错误

### 4. 性能优化
- 合理控制请求频率，避免被反爬虫系统限制
- 缓存商品详情数据，避免重复请求
- 使用异步请求提高并发性能

## 技术支持

如有问题，请联系技术支持团队。
