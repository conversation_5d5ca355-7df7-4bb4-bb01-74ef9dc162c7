"""
调用日志API路由
"""
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from sqlalchemy import func, desc, and_
from typing import List, Optional
from datetime import datetime, date, timezone, timedelta
from pydantic import BaseModel

from app.database import get_db
from app.models import ApiCallLog
from app.utils.response import success_response, error_response

router = APIRouter(prefix="/api/v1/logs", tags=["调用日志"])

# Pydantic模型
class LogResponse(BaseModel):
    id: int
    request_id: str
    client_ip: str
    user_agent: Optional[str]
    request_method: str
    request_path: str
    platform: Optional[str]
    search_type: Optional[str]
    search_query: Optional[str]
    response_code: int
    response_time_ms: int
    response_size: Optional[int]
    proxy_used: Optional[str]
    account_used: Optional[str]
    translation_used: Optional[str]
    error_type: Optional[str]
    error_message: Optional[str]
    created_at: datetime

    class Config:
        from_attributes = True

class LogStats(BaseModel):
    total_requests: int
    success_requests: int
    error_requests: int
    success_rate: float
    avg_response_time: float
    top_platforms: List[dict]
    top_ips: List[dict]
    hourly_distribution: List[dict]

@router.get("/")
async def get_logs(
    db: Session = Depends(get_db),
    start_date: Optional[date] = Query(None, description="开始日期"),
    end_date: Optional[date] = Query(None, description="结束日期"),
    platform: Optional[str] = Query(None, description="平台筛选"),
    client_ip: Optional[str] = Query(None, description="IP筛选"),
    response_code: Optional[int] = Query(None, description="响应码筛选"),
    search_query: Optional[str] = Query(None, description="搜索关键词"),
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(50, ge=1, le=200, description="每页数量")
):
    """获取调用日志列表"""
    try:
        query = db.query(ApiCallLog)
        
        # 时间筛选
        if start_date:
            query = query.filter(func.date(ApiCallLog.created_at) >= start_date)
        if end_date:
            query = query.filter(func.date(ApiCallLog.created_at) <= end_date)
        
        # 其他筛选条件
        if platform:
            query = query.filter(ApiCallLog.platform == platform)
        if client_ip:
            query = query.filter(ApiCallLog.client_ip == client_ip)
        if response_code:
            query = query.filter(ApiCallLog.response_code == response_code)
        if search_query:
            query = query.filter(ApiCallLog.search_query.contains(search_query))
        
        # 获取总数
        total = query.count()

        # 分页
        offset = (page - 1) * size
        logs = query.order_by(desc(ApiCallLog.created_at)).offset(offset).limit(size).all()

        # 返回分页信息
        return success_response({
            "items": logs,
            "total": total,
            "page": page,
            "size": size,
            "pages": (total + size - 1) // size  # 向上取整
        }, "获取日志列表成功")
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取日志列表失败: {str(e)}")

@router.get("/stats", response_model=LogStats)
async def get_logs_stats(
    db: Session = Depends(get_db),
    start_date: Optional[date] = Query(None, description="开始日期"),
    end_date: Optional[date] = Query(None, description="结束日期")
):
    """获取日志统计信息"""
    try:
        # 构建基础查询
        base_query = db.query(ApiCallLog)
        
        # 时间筛选
        if start_date:
            base_query = base_query.filter(func.date(ApiCallLog.created_at) >= start_date)
        if end_date:
            base_query = base_query.filter(func.date(ApiCallLog.created_at) <= end_date)
        else:
            # 默认查询最近7天
            end_date = date.today()
            start_date = end_date - timedelta(days=7)
            base_query = base_query.filter(func.date(ApiCallLog.created_at) >= start_date)
        
        # 总请求数
        total_requests = base_query.count()
        
        # 成功请求数
        success_requests = base_query.filter(ApiCallLog.response_code == 200).count()
        
        # 错误请求数
        error_requests = total_requests - success_requests
        
        # 成功率
        success_rate = (success_requests / total_requests * 100) if total_requests > 0 else 0
        
        # 平均响应时间
        avg_response_time = base_query.with_entities(func.avg(ApiCallLog.response_time_ms)).scalar() or 0
        
        # 热门平台
        top_platforms = base_query.filter(ApiCallLog.platform_id.isnot(None)).with_entities(
            ApiCallLog.platform_id,
            func.count(ApiCallLog.id).label('count')
        ).group_by(ApiCallLog.platform_id).order_by(desc('count')).limit(5).all()
        
        # 热门IP
        top_ips = base_query.with_entities(
            ApiCallLog.client_ip,
            func.count(ApiCallLog.id).label('count')
        ).group_by(ApiCallLog.client_ip).order_by(desc('count')).limit(10).all()
        
        # 按小时分布
        hourly_distribution = base_query.with_entities(
            func.hour(ApiCallLog.created_at).label('hour'),
            func.count(ApiCallLog.id).label('count')
        ).group_by(func.hour(ApiCallLog.created_at)).order_by('hour').all()
        
        return LogStats(
            total_requests=total_requests,
            success_requests=success_requests,
            error_requests=error_requests,
            success_rate=round(success_rate, 2),
            avg_response_time=round(float(avg_response_time), 2),
            top_platforms=[
                {"platform": f"platform_{platform_id}", "count": count}
                for platform_id, count in top_platforms
            ],
            top_ips=[
                {"ip": ip, "count": count} 
                for ip, count in top_ips
            ],
            hourly_distribution=[
                {"hour": hour, "count": count} 
                for hour, count in hourly_distribution
            ]
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取统计信息失败: {str(e)}")

@router.get("/{log_id}", response_model=LogResponse)
async def get_log(log_id: int, db: Session = Depends(get_db)):
    """获取单个日志详情"""
    log = db.query(ApiCallLog).filter(ApiCallLog.id == log_id).first()
    if not log:
        raise HTTPException(status_code=404, detail="日志不存在")
    return log

@router.delete("/cleanup")
async def cleanup_old_logs(
    db: Session = Depends(get_db),
    days: int = Query(30, ge=1, le=365, description="保留天数")
):
    """清理旧日志"""
    try:
        # 计算删除日期
        cutoff_date = datetime.now(timezone.utc) - timedelta(days=days)
        
        # 删除旧日志
        deleted_count = db.query(ApiCallLog).filter(
            ApiCallLog.created_at < cutoff_date
        ).delete()
        
        db.commit()
        
        return {
            "message": f"成功清理 {deleted_count} 条日志",
            "deleted_count": deleted_count,
            "cutoff_date": cutoff_date,
            "retention_days": days
        }
        
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"清理日志失败: {str(e)}")

@router.get("/export/csv")
async def export_logs_csv(
    db: Session = Depends(get_db),
    start_date: Optional[date] = Query(None, description="开始日期"),
    end_date: Optional[date] = Query(None, description="结束日期"),
    platform: Optional[str] = Query(None, description="平台筛选")
):
    """导出日志为CSV"""
    try:
        from fastapi.responses import StreamingResponse
        import csv
        import io
        
        # 构建查询
        query = db.query(ApiCallLog)
        
        if start_date:
            query = query.filter(func.date(ApiCallLog.created_at) >= start_date)
        if end_date:
            query = query.filter(func.date(ApiCallLog.created_at) <= end_date)
        if platform:
            query = query.filter(ApiCallLog.platform_id == platform)
        
        logs = query.order_by(desc(ApiCallLog.created_at)).limit(10000).all()  # 限制导出数量
        
        # 创建CSV内容
        output = io.StringIO()
        writer = csv.writer(output)
        
        # 写入表头
        writer.writerow([
            'ID', '请求ID', '客户端IP', '请求方法', '请求路径', '平台ID',
            '搜索类型', '搜索关键词', '响应码', '响应时间(ms)', '代理',
            '账号', '翻译服务', '错误类型', '错误信息', '创建时间'
        ])
        
        # 写入数据
        for log in logs:
            writer.writerow([
                log.id, log.request_id, log.client_ip, log.request_method,
                log.request_path, log.platform_id, log.search_type, log.search_query,
                log.response_code, log.response_time_ms, log.proxy_used,
                log.account_used, log.translation_used, log.error_type,
                log.error_message, log.created_at.strftime('%Y-%m-%d %H:%M:%S')
            ])
        
        output.seek(0)
        
        # 返回CSV文件
        return StreamingResponse(
            io.BytesIO(output.getvalue().encode('utf-8-sig')),  # 使用BOM以支持中文
            media_type="text/csv",
            headers={"Content-Disposition": f"attachment; filename=api_logs_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"}
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"导出日志失败: {str(e)}")

@router.get("/trends/daily")
async def get_daily_trends(
    db: Session = Depends(get_db),
    days: int = Query(7, ge=1, le=30, description="查询天数")
):
    """获取每日趋势数据"""
    try:
        # 计算开始日期
        end_date = date.today()
        start_date = end_date - timedelta(days=days-1)
        
        # 按日期分组统计
        daily_stats = db.query(
            func.date(ApiCallLog.created_at).label('date'),
            func.count(ApiCallLog.id).label('total_requests'),
            func.sum(func.case([(ApiCallLog.response_code == 200, 1)], else_=0)).label('success_requests'),
            func.avg(ApiCallLog.response_time_ms).label('avg_response_time')
        ).filter(
            func.date(ApiCallLog.created_at) >= start_date,
            func.date(ApiCallLog.created_at) <= end_date
        ).group_by(func.date(ApiCallLog.created_at)).order_by('date').all()
        
        # 格式化结果
        trends = []
        for stat in daily_stats:
            success_rate = (stat.success_requests / stat.total_requests * 100) if stat.total_requests > 0 else 0
            trends.append({
                "date": stat.date.strftime('%Y-%m-%d'),
                "total_requests": stat.total_requests,
                "success_requests": stat.success_requests,
                "success_rate": round(success_rate, 2),
                "avg_response_time": round(float(stat.avg_response_time or 0), 2)
            })
        
        return {
            "period": f"{start_date} 至 {end_date}",
            "trends": trends
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取趋势数据失败: {str(e)}")

@router.get("/errors/analysis")
async def get_error_analysis(
    db: Session = Depends(get_db),
    days: int = Query(7, ge=1, le=30, description="分析天数")
):
    """获取错误分析"""
    try:
        # 计算开始日期
        start_date = date.today() - timedelta(days=days)
        
        # 错误类型统计
        error_types = db.query(
            ApiCallLog.error_type,
            func.count(ApiCallLog.id).label('count')
        ).filter(
            func.date(ApiCallLog.created_at) >= start_date,
            ApiCallLog.error_type.isnot(None)
        ).group_by(ApiCallLog.error_type).order_by(desc('count')).all()
        
        # 响应码统计
        response_codes = db.query(
            ApiCallLog.response_code,
            func.count(ApiCallLog.id).label('count')
        ).filter(
            func.date(ApiCallLog.created_at) >= start_date
        ).group_by(ApiCallLog.response_code).order_by(desc('count')).all()
        
        # 平台错误统计
        platform_errors = db.query(
            ApiCallLog.platform,
            func.count(ApiCallLog.id).label('count')
        ).filter(
            func.date(ApiCallLog.created_at) >= start_date,
            ApiCallLog.response_code != 200
        ).group_by(ApiCallLog.platform).order_by(desc('count')).all()
        
        return {
            "analysis_period": f"最近{days}天",
            "error_types": [
                {"type": error_type, "count": count} 
                for error_type, count in error_types
            ],
            "response_codes": [
                {"code": code, "count": count} 
                for code, count in response_codes
            ],
            "platform_errors": [
                {"platform": platform, "count": count} 
                for platform, count in platform_errors
            ]
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取错误分析失败: {str(e)}")
