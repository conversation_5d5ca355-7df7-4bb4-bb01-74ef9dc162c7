#!/usr/bin/env python3
"""
环境管理脚本
用于切换开发、测试、生产环境配置
"""

import os
import sys
import shutil
import time
from pathlib import Path

class EnvironmentManager:
    def __init__(self):
        self.backend_dir = Path(__file__).parent.parent / "backend"
        self.env_files = {
            "dev": self.backend_dir / ".env.development",
            "test": self.backend_dir / ".env.testing", 
            "prod": self.backend_dir / ".env.production"
        }
        self.current_env = self.backend_dir / ".env"
        
    def create_env_templates(self):
        """创建环境配置模板"""
        
        # 开发环境模板
        dev_template = """# 开发环境配置
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=DEBUG

# 数据库配置
DATABASE_URL=mysql+pymysql://root:password@localhost:3306/aqentcrawler_dev
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_USER=root
MYSQL_PASSWORD=password
MYSQL_DATABASE=aqentcrawler_dev

# Redis配置
REDIS_URL=redis://localhost:6379/1
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=1

# 安全配置
JWT_SECRET_KEY=dev-secret-key-change-this
ENCRYPTION_KEY=dev-encryption-key-32-chars-long

# 管理员账号
ADMIN_USERNAME=admin
ADMIN_PASSWORD=admin123

# 服务配置
HOST=127.0.0.1
PORT=8000
WORKERS=1

# Chrome配置 (Windows)
CHROME_PATH=C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe
CHROME_USER_DATA_DIR=C:\\AgentCrawler\\Temp

# 缓存配置
CACHE_TTL=300
SEARCH_CACHE_TTL=180
DETAIL_CACHE_TTL=600
"""

        # 测试环境模板
        test_template = """# 测试环境配置
ENVIRONMENT=testing
DEBUG=false
LOG_LEVEL=INFO

# 数据库配置
DATABASE_URL=mysql+pymysql://test_user:test_password@test-server:3306/aqentcrawler_test
MYSQL_HOST=test-server
MYSQL_PORT=3306
MYSQL_USER=test_user
MYSQL_PASSWORD=test_password
MYSQL_DATABASE=aqentcrawler_test

# Redis配置
REDIS_URL=redis://test-server:6379/2
REDIS_HOST=test-server
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=2

# 安全配置
JWT_SECRET_KEY=test-secret-key-change-this-too
ENCRYPTION_KEY=test-encryption-key-32-chars-long

# 管理员账号
ADMIN_USERNAME=admin
ADMIN_PASSWORD=test_admin_password

# 服务配置
HOST=0.0.0.0
PORT=8000
WORKERS=2

# Chrome配置 (Linux)
CHROME_PATH=/usr/bin/google-chrome
CHROME_USER_DATA_DIR=/home/<USER>/chrome_data

# 缓存配置
CACHE_TTL=1800
SEARCH_CACHE_TTL=900
DETAIL_CACHE_TTL=3600
"""

        # 生产环境模板
        prod_template = """# 生产环境配置
ENVIRONMENT=production
DEBUG=false
LOG_LEVEL=WARNING

# 数据库配置
DATABASE_URL=mysql+pymysql://aqentcrawler:your_secure_password@localhost:3306/aqentcrawler
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_USER=aqentcrawler
MYSQL_PASSWORD=your_secure_password
MYSQL_DATABASE=aqentcrawler

# Redis配置
REDIS_URL=redis://localhost:6379/0
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# 安全配置 (请修改为随机密钥)
JWT_SECRET_KEY=production-super-secret-jwt-key-change-this-in-production
ENCRYPTION_KEY=production-encryption-key-32-chars-long

# 管理员账号
ADMIN_USERNAME=admin
ADMIN_PASSWORD=your_secure_admin_password

# 服务配置
HOST=0.0.0.0
PORT=8000
WORKERS=4

# Chrome配置 (Linux)
CHROME_PATH=/usr/bin/google-chrome
CHROME_USER_DATA_DIR=/home/<USER>/chrome_data

# 日志配置
LOG_FILE=/home/<USER>/logs/app.log

# 缓存配置
CACHE_TTL=3600
SEARCH_CACHE_TTL=1800
DETAIL_CACHE_TTL=7200

# 代理配置（可选）
PROXY_SERVER=
PROXY_USERNAME=
PROXY_PASSWORD=
"""

        templates = {
            "dev": dev_template,
            "test": test_template,
            "prod": prod_template
        }
        
        for env_name, template in templates.items():
            env_file = self.env_files[env_name]
            if not env_file.exists():
                with open(env_file, 'w', encoding='utf-8') as f:
                    f.write(template)
                print(f"✅ 创建环境模板: {env_file}")
            else:
                print(f"⚠️  环境模板已存在: {env_file}")
    
    def switch_env(self, env_name):
        """切换环境"""
        if env_name not in self.env_files:
            raise ValueError(f"未知环境: {env_name}，支持的环境: {list(self.env_files.keys())}")
        
        source_file = self.env_files[env_name]
        if not source_file.exists():
            print(f"❌ 环境配置文件不存在: {source_file}")
            print("正在创建环境模板...")
            self.create_env_templates()
            if not source_file.exists():
                raise FileNotFoundError(f"无法创建环境配置文件: {source_file}")
        
        # 备份当前配置
        if self.current_env.exists():
            backup_file = self.backend_dir / f".env.backup.{int(time.time())}"
            shutil.copy2(self.current_env, backup_file)
            print(f"📁 备份当前配置: {backup_file}")
        
        # 切换配置
        shutil.copy2(source_file, self.current_env)
        print(f"✅ 已切换到 {env_name} 环境")
        
        # 显示重要提醒
        if env_name == "prod":
            print("\n⚠️  生产环境提醒:")
            print("1. 请确保修改数据库密码")
            print("2. 请更新JWT_SECRET_KEY和ENCRYPTION_KEY")
            print("3. 请设置安全的管理员密码")
            print("4. 请检查Chrome路径配置")
    
    def get_current_env(self):
        """获取当前环境"""
        if not self.current_env.exists():
            return "未配置"
        
        try:
            with open(self.current_env, 'r', encoding='utf-8') as f:
                content = f.read()
                
            if 'ENVIRONMENT=development' in content:
                return "development"
            elif 'ENVIRONMENT=testing' in content:
                return "testing"
            elif 'ENVIRONMENT=production' in content:
                return "production"
            else:
                return "未知"
        except Exception as e:
            return f"读取失败: {e}"
    
    def validate_env(self, env_name=None):
        """验证环境配置"""
        if env_name:
            env_file = self.env_files.get(env_name)
        else:
            env_file = self.current_env
            
        if not env_file or not env_file.exists():
            print("❌ 环境配置文件不存在")
            return False
        
        required_vars = [
            'ENVIRONMENT', 'DATABASE_URL', 'REDIS_URL',
            'JWT_SECRET_KEY', 'ENCRYPTION_KEY', 'CHROME_PATH'
        ]
        
        try:
            with open(env_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            missing_vars = []
            for var in required_vars:
                if f"{var}=" not in content:
                    missing_vars.append(var)
            
            if missing_vars:
                print(f"❌ 缺少必需的环境变量: {', '.join(missing_vars)}")
                return False
            
            print("✅ 环境配置验证通过")
            return True
            
        except Exception as e:
            print(f"❌ 验证环境配置失败: {e}")
            return False
    
    def show_env_info(self):
        """显示环境信息"""
        current = self.get_current_env()
        print(f"\n📊 环境信息:")
        print(f"当前环境: {current}")
        print(f"配置文件: {self.current_env}")
        
        print(f"\n📁 可用环境:")
        for env_name, env_file in self.env_files.items():
            status = "✅" if env_file.exists() else "❌"
            print(f"  {env_name}: {status} {env_file}")

def main():
    manager = EnvironmentManager()
    
    if len(sys.argv) < 2:
        manager.show_env_info()
        print(f"\n使用方法:")
        print(f"  python {sys.argv[0]} [dev|test|prod]     # 切换环境")
        print(f"  python {sys.argv[0]} validate [env]      # 验证环境配置")
        print(f"  python {sys.argv[0]} create              # 创建环境模板")
        sys.exit(0)
    
    command = sys.argv[1]
    
    try:
        if command in ["dev", "test", "prod"]:
            manager.switch_env(command)
        elif command == "validate":
            env_name = sys.argv[2] if len(sys.argv) > 2 else None
            manager.validate_env(env_name)
        elif command == "create":
            manager.create_env_templates()
        elif command == "info":
            manager.show_env_info()
        else:
            print(f"❌ 未知命令: {command}")
            sys.exit(1)
            
    except Exception as e:
        print(f"❌ 操作失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
